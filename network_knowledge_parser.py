#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络事件分析知识库解析器
用于解析知识库文档并根据案例ID返回对应的案例信息
"""

import re
import json
from typing import List, Dict, Optional, Union


class NetworkKnowledgeParser:
    """网络事件分析知识库解析器"""
    
    def __init__(self):
        """初始化解析器，包含知识库内容"""
        self.knowledge_content = """# 网络事件分析知识库
## 网络可用性 - 连接失败场景

### 知识库: NET-AVAIL_CONNECT-FAILED_1
**类别：** 网络可用性/连接失败/未完成三次握手情况
**排查过程：**
1. 查询TCP会话: 连接建立服务器无响应率(server_connection_noresponse_rate), 服务器数据包数(server_total_packet)
   1.1 确认结果: 连接建立服务器无响应率 == 100% AND 服务器数据包数 == 0 则满足条件

**根因分析指向：**
- 网络路径问题： SYN包在去往服务器的路上被丢弃（如网络拥塞、路由黑洞）
- 防火墙静默丢弃： 防火墙配置了DROP规则，它"假装没看见"SYN包，不作任何回应
- 服务器无响应： SYN包到达了服务器，但服务器的SYN/ACK响应在返回途中丢失了
- 服务器SYN队列溢出： 服务器因负载过高或遭受SYN Flood攻击，其用于处理新连接的队列已满，直接丢弃了新的SYN包

### 知识库: NET-AVAIL_CONNECT-FAILED_2
**类别：** 网络可用性/连接失败/SYN和SYNACK不匹配
**排查过程：**
1. 在线TCP交易解码: 解码该会话前10个包，时间范围为会话创建时间前2s，limit10个包
   1.1 确认结果： SYN的ACK不是收到的SYN序列号+1则满足条件

**根因分析指向：**
- 有状态网络设备故障： 路径上的负载均衡器、NAT网关或防火墙的状态表发生错乱，将不相关的会话信息串在了一起
- 非对称路由： 请求和响应走了不同的路径，导致路径上的状态设备无法正确匹配会话
- 中间人攻击 (MitM)： 有攻击者在中间伪造SYN/ACK包，企图劫持或干扰连接
- 服务器TCP协议栈Bug： 极其罕见，但理论上可能存在

### 知识库: NET-AVAIL_CONNECT-FAILED_3
**类别：** 网络可用性/连接失败/SYN包发送收到RST
**排查过程：**
1. 查询TCP会话: 连接建立服务器重置次数(server_connection_rst), 连接建立服务器重置率(server_connection_rst_rate)
   1.1 确认结果: 连接建立服务器重置次数 > 0 AND 连接建立服务器重置率 > 0 则满足条件

**根因分析指向：**
- 服务器端口未监听： 这是最常见的原因。服务器上根本没有应用程序在等待这个端口的连接
- 防火墙策略： 中间或服务器上的防火墙配置了REJECT规则，它主动地拒绝了该连接请求，并回复了一个RST包

### 知识库: NET-AVAIL_CONNECT-FAILED_4
**类别：** 网络可用性/连接失败/服务器端口不通
**排查过程：**
1. 查询TCP会话: TCP同步包(tcp_syn_packet), 服务器TCP重置包(server_tcp_rst_packet), 连接建立成功次数(connection_succ_count)
   1.1 确认结果: TCP同步包 > 0 AND 服务器TCP重置包 > 0 AND 连接建立成功次数 == 0 则满足条件

**根因分析指向：**
- 服务器端口未监听：服务器上没有应用程序在监听该端口
- 服务器操作系统内核直接回复RST包：表明这是一个网络连接层面的可用性问题

### 知识库: NET-AVAIL_CONNECT-FAILED_5A
**类别：** 网络可用性/连接失败/防火墙策略-静默丢弃
**排查过程：**
1. 查询TCP会话: TCP同步包(tcp_syn_packet), TCP同步重传包(tcp_syn_retrans_packet), 服务器数据包数(server_total_packet), 连接建立成功次数(connection_succ_count)
   1.1 确认结果: TCP同步包 > 0 AND TCP同步重传包 > 0 AND 服务器数据包数 == 0 AND 连接建立成功次数 == 0 则满足条件

**根因分析指向：**
- 防火墙静默丢弃：防火墙配置了DROP规则，直接将SYN包丢弃，不作任何回应
- 网络路径问题：SYN包在传输过程中被丢弃

### 知识库: NET-AVAIL_CONNECT-FAILED_5B
**类别：** 网络可用性/连接失败/防火墙策略-拒绝
**排查过程：**
1. 查询TCP会话: TCP同步包(tcp_syn_packet), 服务器TCP重置包(server_tcp_rst_packet), 连接建立服务器重置次数(server_connection_rst)
   1.1 确认结果: TCP同步包 > 0 AND 服务器TCP重置包 > 0 AND 连接建立服务器重置次数 > 0 则满足条件

**根因分析指向：**
- 防火墙拒绝策略：防火墙配置了REJECT规则，主动回复TCP RST包或ICMP Port Unreachable消息
- 连接被直接拒绝：防火墙明确拒绝了连接请求

### 知识库: NET-AVAIL_CONNECT-FAILED_6
**类别：** 网络可用性/连接失败/服务器负载过高
**排查过程：**
1. 查询TCP会话: 三次握手次数(tcp_three_handshake), 服务器TCP窗口为0次数(server_tcp_window_0), TCP交易无响应次数(tcp_transaction_no_response_count), 连接建立成功次数(connection_succ_count)
   1.1 确认结果: 三次握手次数 > 0 AND 连接建立成功次数 > 0 AND (服务器TCP窗口为0次数 > 0 OR TCP交易无响应次数 > 0) 则满足条件

**根因分析指向：**
- 服务器应用层负载过高：CPU满载、线程池耗尽等原因导致应用程序无法处理新连接
- 服务器接收窗口持续为0：暗示服务器的应用层已经无法接收更多数据
- TCP连接成功但应用层长时间无数据交互：表现为"伪连接成功"的场景
"""
        self._parse_knowledge_base()
    
    def _parse_knowledge_base(self):
        """解析知识库内容，提取案例信息"""
        self.cases = {}
        
        # 使用正则表达式匹配知识库条目
        pattern = r'### 知识库: NET-AVAIL_CONNECT-FAILED_(\w+)\n(.*?)(?=### 知识库:|$)'
        matches = re.findall(pattern, self.knowledge_content, re.DOTALL)
        
        for case_id, content in matches:
            self.cases[case_id] = self._parse_case_content(content.strip())
    
    def _parse_case_content(self, content: str) -> Dict[str, str]:
        """解析单个案例的内容"""
        case_info = {}
        
        # 提取类别
        category_match = re.search(r'\*\*类别：\*\* (.+)', content)
        if category_match:
            case_info['category'] = category_match.group(1)
        
        # 提取排查过程
        process_match = re.search(r'\*\*排查过程：\*\*(.*?)\*\*根因分析指向：\*\*', content, re.DOTALL)
        if process_match:
            case_info['process'] = process_match.group(1).strip()
        
        # 提取根因分析
        analysis_match = re.search(r'\*\*根因分析指向：\*\*(.*?)$', content, re.DOTALL)
        if analysis_match:
            case_info['analysis'] = analysis_match.group(1).strip()
        
        return case_info
    
    def get_cases_by_ids(self, case_ids: Union[str, List[str]]) -> Dict[str, Dict[str, str]]:
        """
        根据案例ID列表返回对应的案例信息

        Args:
            case_ids: 案例ID列表，可以是字符串格式 '["1", "2", "5A"]' 或列表格式 ["1", "2", "5A"]
                     也支持完整的知识库名称，如 "NET-AVAIL_CONNECT-FAILED_1"

        Returns:
            包含案例信息的字典，格式为 {case_id: case_info}
        """
        # 如果输入是字符串，尝试解析为列表
        if isinstance(case_ids, str):
            try:
                # 首先尝试标准JSON解析
                case_ids = json.loads(case_ids)
            except json.JSONDecodeError:
                try:
                    # 如果失败，尝试将单引号替换为双引号后再解析
                    case_ids_fixed = case_ids.replace("'", '"')
                    case_ids = json.loads(case_ids_fixed)
                except json.JSONDecodeError:
                    # 返回错误信息，但保持正确的数据结构
                    return {"PARSE_ERROR": {"error": f"无法解析案例ID字符串: {case_ids}"}}

        # 确保case_ids是列表
        if not isinstance(case_ids, list):
            # 返回错误信息，但保持正确的数据结构
            return {"TYPE_ERROR": {"error": f"案例ID必须是列表格式，当前类型: {type(case_ids)}"}}

        result = {}

        for case_id in case_ids:
            # 处理完整的知识库名称，提取简化的案例ID
            simplified_id = self._extract_case_id(case_id)

            if simplified_id in self.cases:
                result[case_id] = self.cases[simplified_id]
            else:
                result[case_id] = {"error": f"案例 {case_id} 不存在"}

        return result

    def _extract_case_id(self, full_name: str) -> str:
        """
        从完整的知识库名称中提取简化的案例ID
        例如：NET-AVAIL_CONNECT-FAILED_1 -> 1
        """
        if full_name.startswith("NET-AVAIL_CONNECT-FAILED_"):
            return full_name.replace("NET-AVAIL_CONNECT-FAILED_", "")
        return full_name
    
    def get_all_case_ids(self) -> List[str]:
        """获取所有可用的案例ID"""
        return list(self.cases.keys())
    
    def format_case_output(self, case_id: str, case_info: Dict[str, str]) -> str:
        """格式化单个案例的输出"""
        if "error" in case_info:
            return f"案例 {case_id}: {case_info['error']}"

        # 如果case_id已经包含完整前缀，直接使用；否则添加前缀
        if case_id.startswith("NET-AVAIL_CONNECT-FAILED_"):
            display_case_id = case_id
        else:
            display_case_id = f"NET-AVAIL_CONNECT-FAILED_{case_id}"

        output = f"=== 案例 {display_case_id} ===\n"
        output += f"类别: {case_info.get('category', '未知')}\n\n"
        output += f"排查过程:\n{case_info.get('process', '无')}\n\n"
        output += f"根因分析指向:\n{case_info.get('analysis', '无')}\n"
        output += "=" * 50 + "\n"

        return output


def main(case_ids):
    """示例用法"""
    parser = NetworkKnowledgeParser()

   
    cases = parser.get_cases_by_ids(case_ids)

    result = ""
    for case_id, case_info in cases.items():
        result += parser.format_case_output(case_id, case_info)

    return {
        "result" : result
    }
    
print(main("['NET-AVAIL_CONNECT-FAILED_2']"))