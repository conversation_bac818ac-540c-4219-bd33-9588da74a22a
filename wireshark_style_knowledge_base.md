# Wireshark风格的TCP解码表达式知识库

## 概述

现在支持类似Wireshark的灵活表达式语法，用户可以自定义复杂的TCP包分析条件，而不需要预定义固定的指标。

## 表达式语法

### 基本语法
```
变量名 = find_pkt(条件); 另一个变量 = find_pkt(另一个条件); 最终条件表达式
```

### 支持的TCP字段
- `tcp.flag.syn` - SYN标志位 (1/0)
- `tcp.flag.ack` - ACK标志位 (1/0)  
- `tcp.flag.rst` - RST标志位 (1/0)
- `tcp.flag.fin` - FIN标志位 (1/0)
- `tcp.flag.psh` - PSH标志位 (1/0)
- `tcp.flag.urg` - URG标志位 (1/0)
- `tcp.seq` - 序列号
- `tcp.ack` - 确认号
- `tcp.len` - 载荷长度
- `tcp.window` - 窗口大小
- `direction` - 包方向 (1=客户端→服务器, 2=服务器→客户端)

### 包查找函数
- `find_pkt(条件)` - 查找第一个满足条件的包
- `find_pkts(条件)` - 查找所有满足条件的包（在Python代码中使用）

### 逻辑运算符
- `&&` - 逻辑与
- `||` - 逻辑或
- `==` - 等于
- `!=` - 不等于
- `>`, `<`, `>=`, `<=` - 比较运算符

## 知识库示例

### 1. SYN/ACK序列号不匹配检测
```
syn_pkt = find_pkt(tcp.flag.syn == 1 && tcp.flag.ack != 1);
syn_ack_pkt = find_pkt(tcp.flag.syn == 1 && tcp.flag.ack == 1);
syn_ack_pkt.ack != syn_pkt.seq + 1
```

### 2. TCP重置包检测
```
rst_pkt = find_pkt(tcp.flag.rst == 1);
rst_pkt.seq >= 0
```

### 3. 三次握手完成检测
```
syn_pkt = find_pkt(tcp.flag.syn == 1 && tcp.flag.ack != 1);
syn_ack_pkt = find_pkt(tcp.flag.syn == 1 && tcp.flag.ack == 1);
ack_pkt = find_pkt(tcp.flag.ack == 1 && tcp.flag.syn != 1);
ack_pkt.seq == syn_pkt.seq + 1 && ack_pkt.ack == syn_ack_pkt.seq + 1
```

### 4. 数据传输检测
```
data_pkt = find_pkt(tcp.len > 0);
data_pkt.len > 0
```

### 5. SYN重传检测
```
syn_pkt = find_pkts(tcp.flag.syn == 1 && tcp.flag.ack != 1);
len(syn_pkt) > 1 && syn_pkt[0].seq == syn_pkt[1].seq
```

### 6. 服务器无响应检测
```
syn_pkt = find_pkt(tcp.flag.syn == 1 && direction == 1);
server_pkt = find_pkt(direction == 2);
server_pkt.seq == 0
```

### 7. 窗口为0检测
```
zero_win_pkt = find_pkt(tcp.window == 0);
zero_win_pkt.window == 0
```

### 8. 复杂条件：连接被拒绝
```
syn_pkt = find_pkt(tcp.flag.syn == 1 && tcp.flag.ack != 1);
rst_pkt = find_pkt(tcp.flag.rst == 1 && direction == 2);
rst_pkt.ack == syn_pkt.seq + 1
```

## 使用示例

### Python代码示例
```python
from data_preprocessor import NetworkDataPreprocessor

# 创建预处理器
preprocessor = NetworkDataPreprocessor()

# 解析TCP交易解码数据
preprocessor._parse_tcp_transaction_data(tcp_decode_data)

# 使用Wireshark风格表达式
expr = """
syn_pkt = find_pkt(tcp.flag.syn == 1 && tcp.flag.ack != 1);
syn_ack_pkt = find_pkt(tcp.flag.syn == 1 && tcp.flag.ack == 1);
syn_ack_pkt.ack != syn_pkt.seq + 1
"""

result = preprocessor.evaluate_tcp_decode_expr(expr)
print(f"SYN/ACK不匹配: {result}")
```

### 知识库配置示例
```python
{
    'id': 'NET-AVAIL_CONNECT-FAILED_2',
    'category': '网络可用性/连接失败/SYN和SYNACK不匹配',
    'query_type': '在线TCP交易解码',
    'required_fields': [],  # 不需要预定义字段
    'condition_expr': 'syn_pkt = find_pkt(tcp.flag.syn == 1 && tcp.flag.ack != 1); syn_ack_pkt = find_pkt(tcp.flag.syn == 1 && tcp.flag.ack == 1); syn_ack_pkt.ack != syn_pkt.seq + 1',
    'decode_params': '解码该会话前10个包，时间范围为会话创建时间前2s，limit10个包'
}
```

## 优势

### 1. 灵活性
- 不需要预定义固定的指标
- 可以根据实际需求编写任意复杂的条件
- 支持动态包查找和字段访问

### 2. 可读性
- 类似Wireshark的语法，网络工程师容易理解
- 表达式直观地描述了要检测的网络行为
- 支持复杂的逻辑组合

### 3. 扩展性
- 容易添加新的TCP字段支持
- 可以扩展支持其他协议字段
- 支持自定义函数和条件

### 4. 准确性
- 直接基于包级别的分析
- 避免了预计算指标可能的误差
- 支持精确的序列号和标志位检查

## 注意事项

1. **包不存在处理**: 如果`find_pkt()`没有找到匹配的包，相关的条件会返回False
2. **性能考虑**: 复杂的表达式可能需要遍历多次包序列
3. **字段访问**: 确保访问的字段在包数据中存在
4. **表达式复杂度**: 建议将复杂逻辑分解为多个简单的条件

## 扩展计划

1. 支持更多协议字段（IP、UDP等）
2. 添加时间相关的函数（包间隔、超时检测等）
3. 支持统计函数（count、sum、avg等）
4. 添加正则表达式匹配支持
