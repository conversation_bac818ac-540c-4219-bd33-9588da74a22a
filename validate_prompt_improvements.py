#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证llm_prompt_template.md的改进点
"""

def validate_prompt_improvements():
    """验证提示词文件的关键改进点"""
    
    try:
        with open('llm_prompt_template.md', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("=== 验证提示词改进点 ===")
        
        # 检查关键改进点
        improvements = [
            {
                "name": "查询类型完整性规则",
                "keywords": ["查询类型完整性", "必须检查所有知识库场景的查询类型"],
                "found": False
            },
            {
                "name": "在线TCP交易解码强制包含规则", 
                "keywords": ["在线TCP交易解码强制包含", "必须在additional_query中包含完整的解码查询"],
                "found": False
            },
            {
                "name": "查询类型强制检查原则",
                "keywords": ["查询类型强制检查原则", "必须遍历所有partial_matches和need_more_info"],
                "found": False
            },
            {
                "name": "在线TCP交易解码必须包含原则",
                "keywords": ["在线TCP交易解码必须包含原则", "如果任何知识库场景需要"],
                "found": False
            },
            {
                "name": "检查清单",
                "keywords": ["检查清单", "是否有场景需要", "在线TCP交易解码"],
                "found": False
            },
            {
                "name": "容易漏掉的场景示例",
                "keywords": ["容易漏掉在线TCP交易解码的场景", "正确的LLM输出"],
                "found": False
            },
            {
                "name": "错误示例说明",
                "keywords": ["错误示例", "漏掉在线TCP交易解码", "这是错误的"],
                "found": False
            }
        ]
        
        # 检查每个改进点
        for improvement in improvements:
            for keyword in improvement["keywords"]:
                if keyword in content:
                    improvement["found"] = True
                    break
        
        # 输出验证结果
        all_found = True
        for improvement in improvements:
            status = "✅" if improvement["found"] else "❌"
            print(f"{status} {improvement['name']}")
            if not improvement["found"]:
                all_found = False
        
        print(f"\n=== 总体结果 ===")
        if all_found:
            print("✅ 所有关键改进点都已添加到提示词中")
        else:
            print("❌ 部分改进点缺失，需要进一步完善")
        
        # 统计改进点
        found_count = sum(1 for imp in improvements if imp["found"])
        total_count = len(improvements)
        print(f"完成度: {found_count}/{total_count} ({found_count/total_count*100:.1f}%)")
        
        return all_found
        
    except FileNotFoundError:
        print("❌ 找不到llm_prompt_template.md文件")
        return False
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        return False

def check_specific_patterns():
    """检查特定的模式和结构"""
    
    try:
        with open('llm_prompt_template.md', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("\n=== 检查特定模式 ===")
        
        # 检查additional_query示例的结构
        patterns = [
            {
                "name": "包含两种查询类型的示例",
                "pattern": '"query": "查询TCP会话:',
                "found": content.count('"query": "查询TCP会话:') >= 2
            },
            {
                "name": "包含在线TCP交易解码的示例", 
                "pattern": '"query": "在线TCP交易解码:',
                "found": content.count('"query": "在线TCP交易解码:') >= 2
            },
            {
                "name": "错误示例说明",
                "pattern": "错误示例",
                "found": "错误示例" in content
            },
            {
                "name": "分析过程说明",
                "pattern": "分析过程：",
                "found": "分析过程：" in content
            }
        ]
        
        for pattern in patterns:
            status = "✅" if pattern["found"] else "❌"
            print(f"{status} {pattern['name']}")
        
        return all(p["found"] for p in patterns)
        
    except Exception as e:
        print(f"❌ 模式检查出错: {e}")
        return False

def generate_summary():
    """生成改进总结"""
    
    print("\n=== 改进总结 ===")
    print("本次对llm_prompt_template.md的主要改进：")
    print()
    print("1. 🎯 强化查询类型完整性检查")
    print("   - 添加了'查询类型完整性'规则")
    print("   - 要求检查所有知识库场景的查询类型")
    print()
    print("2. 🔒 强制包含在线TCP交易解码")
    print("   - 添加了'在线TCP交易解码强制包含'规则")
    print("   - 明确要求必须包含完整的解码查询")
    print()
    print("3. 📋 增加检查清单")
    print("   - 提供了生成additional_query前的检查清单")
    print("   - 确保不遗漏任何查询类型")
    print()
    print("4. 📚 增加详细示例")
    print("   - 添加了容易漏掉TCP交易解码的场景示例")
    print("   - 提供了正确和错误的对比示例")
    print()
    print("5. ⚠️ 强化注意事项")
    print("   - 增加了重要提醒和检查要点")
    print("   - 明确了错误模式和避免方法")

if __name__ == "__main__":
    print("验证llm_prompt_template.md的改进效果...")
    
    # 验证改进点
    improvements_ok = validate_prompt_improvements()
    
    # 检查特定模式
    patterns_ok = check_specific_patterns()
    
    # 生成总结
    generate_summary()
    
    print(f"\n=== 最终结果 ===")
    if improvements_ok and patterns_ok:
        print("✅ 提示词改进验证通过")
        print("💡 建议：运行test_additional_query_fix.py进行实际LLM测试")
    else:
        print("❌ 提示词改进验证失败")
        print("💡 建议：检查并完善缺失的改进点")
