import json
from typing import Dict, List, Any


def main(arg1: str) -> dict:
    """
    解析查询结果JSON字符串，提取各个字段
    
    Args:
        arg1 (str): JSON格式的字符串，包含查询结果信息
        
    Returns:
        dict: 包含提取的字段信息
    """
    try:
        # 解析JSON字符串
        data = json.loads(arg1)
        
        # 提取各个字段，如果字段不存在则使用默认值
        result = {
            "status": data.get("status", ""),
            "full_matches": data.get("full_matches", []),
            "partial_matches": data.get("partial_matches", []),
            "additional_query": data.get("additional_query", []),
            "need_more_info": data.get("need_more_info", []),
            "unmatched": data.get("unmatched", [])
        }
        
        return result
        
    except json.JSONDecodeError as e:
        # 如果JSON解析失败，返回错误信息
        return {
            "error": f"JSON解析失败: {str(e)}",
            "status": "",
            "full_matches": [],
            "partial_matches": [],
            "additional_query": [],
            "need_more_info": [],
            "unmatched": []
        }
    except Exception as e:
        # 处理其他异常
        return {
            "error": f"处理失败: {str(e)}",
            "status": "",
            "full_matches": [],
            "partial_matches": [],
            "additional_query": [],
            "need_more_info": [],
            "unmatched": []
        }


# 测试示例
if __name__ == "__main__":
    # 测试用例
    test_input = '''
    {
      "status": "完全匹配",
      "full_matches": ["NET-AVAIL_CONNECT-FAILED_1"],
      "partial_matches": [],
      "additional_query": [],
      "need_more_info": [],
      "unmatched": []
    }
    '''
    
    result = main(test_input)
    print("解析结果:")
    for key, value in result.items():
        print(f"  {key}: {value}")
