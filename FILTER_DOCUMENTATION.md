# 过滤器说明功能文档

## 概述

为MCP客户端增加了获取过滤器说明的能力，基于10.1下载过滤器文档实现。新增的功能可以帮助用户了解所有支持的过滤器对象和使用方法。

## 新增功能

### 1. get_supported_filters() 工具

在 `stats_mcp_server_official.py` 和 `stats_mcp_server.py` 中都添加了 `get_supported_filters()` MCP工具函数。

**功能描述：**
- 获取支持的过滤器对象和使用说明
- 基于10.1下载过滤器文档的完整实现
- 返回JSON格式的详细过滤器说明

**返回内容包括：**

#### 下载过滤器 (download_filters)
按类别组织的过滤器对象：

1. **IP过滤器 (ip_filters)**
   - `ip_addr`: IP地址过滤
   - `client_ip_addr`: 客户端IP地址过滤
   - `server_ip_addr`: 服务端IP地址过滤
   - `ip_range`: IP范围过滤
   - `ip_flow`: IP会话过滤

2. **端口过滤器 (port_filters)**
   - `port`: 端口过滤
   - `client_port`: 客户端端口过滤
   - `server_port`: 服务端端口过滤
   - `port_range`: 端口范围过滤
   - `ipport_flow`: TCP/UDP会话过滤

3. **物理地址过滤器 (physical_filters)**
   - `phys_addr`: 物理地址过滤
   - `phys_range`: 物理地址范围过滤
   - `phys_flow`: 物理会话过滤

4. **应用过滤器 (application_filters)**
   - `application`: 应用过滤
   - `netsegment`: 网段过滤
   - `netsegment_flow`: 网段会话过滤

5. **协议过滤器 (protocol_filters)**
   - `tree_protocol`: 协议树协议过滤
   - `top_protocol`: 顶层协议过滤

6. **VLAN/MPLS过滤器 (vlan_mpls_filters)**
   - `vlan_id`: VLAN ID过滤
   - `vlan_id_range`: VLAN ID范围过滤
   - `mplsvpn_id`: MPLS VPN标签过滤
   - `mplsvpn_range`: MPLS VPN标签范围过滤

7. **接口过滤器 (interface_filters)**
   - `netflow_id`: Netflow接口ID过滤
   - `netflow_id_range`: Netflow接口ID范围过滤
   - `adapter_id`: 网卡ID过滤
   - `adapter_id_range`: 网卡ID范围过滤

8. **QoS过滤器 (qos_filters)**
   - `dscp`: DSCP值过滤
   - `dscp_range`: DSCP范围过滤

#### 查询过滤器 (query_filters)
用于统计表查询的过滤器：

- **操作符**: =, !=, >=, <=, >, <
- **逻辑操作符**: && (逻辑与), || (逻辑或)
- **URL编码注意事项**: & 需要转义为 %26，&& 需要转义为 %26%26

## 使用示例

### 过滤器示例

```bash
# IP地址过滤
ip_addr=***********

# IP范围过滤
ip_range=***********-*************

# TCP/UDP会话过滤
ipport_flow=[*************]:5652-[*************]:443

# 物理地址过滤
phys_addr=1C:6F:65:97:20:66

# VLAN ID过滤
vlan_id=236

# 应用过滤（需要应用ID）
application=30001
```

### 查询过滤器示例

```bash
# 字节数大于1MB
total_byte>1000000

# 服务端口为80
server_port=80

# 组合条件：字节数大于1MB且端口为443
total_byte>1000000 && server_port=443

# 多端口条件：端口为80或443
server_port=80 || server_port=443
```

## 技术实现

### 文件修改

1. **stats_mcp_server_official.py**
   - 增强了原有的 `get_supported_filters()` 函数
   - 添加了完整的过滤器分类和说明

2. **stats_mcp_server.py**
   - 新增了 `get_supported_filters()` 函数
   - 与官方版本保持一致的功能

### 数据结构

返回的JSON结构包含：
- `description`: 总体描述
- `download_filters`: 下载过滤器分类
- `query_filters`: 查询过滤器说明
- `usage_notes`: 使用注意事项

## 测试验证

创建了 `test_filter_function.py` 测试脚本，验证功能正常工作：

```bash
python3 test_filter_function.py
```

测试结果显示：
- ✅ 过滤器说明功能测试成功
- 包含8个下载过滤器类别
- 包含完整的查询过滤器说明
- 提供了丰富的使用示例

## 使用方法

在MCP客户端中调用 `get_supported_filters` 工具即可获取完整的过滤器说明文档，帮助用户正确使用各种过滤条件。
