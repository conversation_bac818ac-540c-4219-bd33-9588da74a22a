#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整系统测试：结合TCP会话查询和Wireshark风格的TCP解码表达式
"""

import json
from data_preprocessor import analyze_tcp_session_with_knowledge_base, NetworkDataPreprocessor

def test_complete_analysis_workflow():
    """测试完整的分析工作流程"""
    print("=== 完整系统测试 ===")
    
    # 用户输入
    user_input = """
    链路id:2 服务器：192.168.163.209 
    客户端: 10.127.104.10 客户端端口: 40276 服务器: 60.28.208.186 服务器端口: 80 
    会话开始时间: 2025/07/22 13:53:01 
    会话结束时间: 2025/07/22 13:53:02 
    查询TCP会话: tcp_connect_failure_rate = 100%
    """
    
    # 第一步：用户只提供了连接失败率
    tcp_metrics_step1 = {
        'tcp_connect_failure_rate': 100
    }
    
    print("第一步分析：只有连接失败率")
    result_step1 = analyze_tcp_session_with_knowledge_base(user_input, tcp_metrics_step1)
    print(f"状态: {result_step1['status']}")
    print(f"需要补充查询: {len(result_step1['additional_query'])}个")
    for i, query in enumerate(result_step1['additional_query'], 1):
        print(f"  {i}. {query['query']}")
    
    # 第二步：用户补充了TCP会话指标
    tcp_metrics_step2 = {
        'tcp_connect_failure_rate': 100,
        'tcp_syn_packet': 5,
        'tcp_syn_retrans_packet': 3,
        'server_total_packet': 0,
        'connection_succ_count': 0,
        'server_connection_noresponse_rate': 100
    }
    
    print("\n第二步分析：补充TCP会话指标")
    result_step2 = analyze_tcp_session_with_knowledge_base(user_input, tcp_metrics_step2)
    print(f"状态: {result_step2['status']}")
    print(f"完全匹配: {result_step2['full_matches']}")
    print(f"仍需补充查询: {len(result_step2['additional_query'])}个")
    
    # 第三步：用户提供了TCP交易解码数据
    tcp_decode_data = {
        "success": True,
        "message": "TCP交易解析成功",
        "data": {
            "transactions": [
                {
                    "transaction_id": 1,
                    "retransmit": 1,
                    "server_response_time": 0,
                    "request_bytes": 0,
                    "response_bytes": 0,
                    "request_packets": 3,
                    "response_packets": 0,
                    "pkt_seq_diagram": [
                        {
                            "ack": 0,
                            "direction": 1,
                            "seq": 1000,
                            "payloadlen": 0,
                            "summaryDecoder": "<PacketSummary>[SYN]序列号=1000,确认号=0,窗口=14600,头部长度=40 字节</PacketSummary>",
                            "time": 1753163581166521888
                        },
                        {
                            "ack": 0,
                            "direction": 1,
                            "seq": 1000,  # SYN重传
                            "payloadlen": 0,
                            "summaryDecoder": "<PacketSummary>[SYN]序列号=1000,确认号=0,窗口=14600,头部长度=40 字节</PacketSummary>",
                            "time": 1753163581166522000
                        },
                        {
                            "ack": 0,
                            "direction": 1,
                            "seq": 1000,  # 再次SYN重传
                            "payloadlen": 0,
                            "summaryDecoder": "<PacketSummary>[SYN]序列号=1000,确认号=0,窗口=14600,头部长度=40 字节</PacketSummary>",
                            "time": 1753163581166523000
                        }
                    ]
                }
            ]
        }
    }
    
    print("\n第三步分析：加入TCP交易解码数据")
    
    # 创建预处理器并解析TCP解码数据
    preprocessor = NetworkDataPreprocessor()
    preprocessor._parse_tcp_transaction_data(tcp_decode_data)
    
    # 分析知识库匹配（包含TCP解码数据）
    result_step3 = preprocessor.analyze_knowledge_base_matches(tcp_metrics_step2, tcp_decode_data)
    
    print(f"状态: {result_step3['status']}")
    print(f"完全匹配: {result_step3['full_matches']}")
    print(f"TCP解码指标: {result_step3['tcp_decode_metrics']}")
    
    # 测试具体的Wireshark风格表达式
    print("\n=== Wireshark风格表达式测试 ===")
    
    # 测试SYN重传检测
    expr1 = "syn_pkts = find_pkts('tcp.flag.syn == 1'); len(syn_pkts) > 1"  # 这个需要在Python中实现
    # 简化版本：检查是否有SYN包
    expr1_simple = "syn_pkt = find_pkt(tcp.flag.syn == 1); syn_pkt.seq > 0"
    result1 = preprocessor.evaluate_tcp_decode_expr(expr1_simple)
    print(f"存在SYN包: {result1}")
    
    # 检查服务器是否有响应
    expr2 = "server_pkt = find_pkt(direction == 2); server_pkt.seq > 0"
    result2 = preprocessor.evaluate_tcp_decode_expr(expr2)
    print(f"服务器有响应: {result2}")
    
    # 检查是否只有客户端包
    expr3 = "client_pkt = find_pkt(direction == 1); server_pkt = find_pkt(direction == 2); client_pkt.seq > 0"
    result3 = preprocessor.evaluate_tcp_decode_expr(expr3)
    print(f"只有客户端包: {result3}")

def test_custom_knowledge_base():
    """测试自定义知识库条目"""
    print("\n\n=== 自定义知识库测试 ===")
    
    # 创建自定义知识库条目
    custom_kb_item = {
        'id': 'CUSTOM_SYN_FLOOD_DETECTION',
        'category': '自定义/SYN洪水攻击检测',
        'query_type': '在线TCP交易解码',
        'required_fields': [],
        'condition_expr': 'syn_pkt = find_pkt(tcp.flag.syn == 1 && direction == 1); server_pkt = find_pkt(direction == 2); syn_pkt.seq > 0',
        'decode_params': '解码该会话前10个包，时间范围为会话创建时间前2s，limit10个包'
    }
    
    # 模拟SYN洪水攻击的数据（只有客户端SYN，没有服务器响应）
    syn_flood_data = {
        "success": True,
        "message": "TCP交易解析成功",
        "data": {
            "transactions": [
                {
                    "transaction_id": 1,
                    "retransmit": 0,
                    "server_response_time": 0,
                    "request_bytes": 0,
                    "response_bytes": 0,
                    "request_packets": 1,
                    "response_packets": 0,
                    "pkt_seq_diagram": [
                        {
                            "ack": 0,
                            "direction": 1,
                            "seq": 1000,
                            "payloadlen": 0,
                            "summaryDecoder": "<PacketSummary>[SYN]序列号=1000,确认号=0,窗口=14600,头部长度=40 字节</PacketSummary>",
                            "time": 1753163581166521888
                        }
                    ]
                }
            ]
        }
    }
    
    preprocessor = NetworkDataPreprocessor()
    preprocessor._parse_tcp_transaction_data(syn_flood_data)
    
    # 测试自定义表达式
    result = preprocessor.evaluate_tcp_decode_expr(custom_kb_item['condition_expr'])
    print(f"自定义SYN洪水检测: {result}")
    
    # 显示包信息
    print("包序列信息:")
    for i, pkt in enumerate(preprocessor.packets):
        print(f"  包{i+1}: 方向={pkt['direction_desc']}, 标志={pkt['tcp_flags']}, seq={pkt['sequence_number']}")

def test_complex_scenarios():
    """测试复杂场景"""
    print("\n\n=== 复杂场景测试 ===")
    
    # 场景：连接被重置
    reset_scenario_data = {
        "success": True,
        "message": "TCP交易解析成功",
        "data": {
            "transactions": [
                {
                    "transaction_id": 1,
                    "retransmit": 0,
                    "server_response_time": 0,
                    "request_bytes": 0,
                    "response_bytes": 0,
                    "request_packets": 1,
                    "response_packets": 1,
                    "pkt_seq_diagram": [
                        {
                            "ack": 0,
                            "direction": 1,
                            "seq": 1000,
                            "payloadlen": 0,
                            "summaryDecoder": "<PacketSummary>[SYN]序列号=1000,确认号=0,窗口=14600,头部长度=40 字节</PacketSummary>",
                            "time": 1753163581166521888
                        },
                        {
                            "ack": 1001,
                            "direction": 2,
                            "seq": 0,
                            "payloadlen": 0,
                            "summaryDecoder": "<PacketSummary>[RST]序列号=0,确认号=1001,窗口=0,头部长度=20 字节</PacketSummary>",
                            "time": 1753163581166522000
                        }
                    ]
                }
            ]
        }
    }
    
    preprocessor = NetworkDataPreprocessor()
    preprocessor._parse_tcp_transaction_data(reset_scenario_data)
    
    print("连接重置场景包序列:")
    for i, pkt in enumerate(preprocessor.packets):
        print(f"  包{i+1}: 方向={pkt['direction_desc']}, 标志={pkt['tcp_flags']}, seq={pkt['sequence_number']}, ack={pkt['ack_number']}")
    
    # 测试连接被拒绝的检测
    expr = "syn_pkt = find_pkt(tcp.flag.syn == 1 && direction == 1); rst_pkt = find_pkt(tcp.flag.rst == 1 && direction == 2); rst_pkt.ack == syn_pkt.seq + 1"
    result = preprocessor.evaluate_tcp_decode_expr(expr)
    print(f"连接被服务器拒绝: {result}")

if __name__ == "__main__":
    test_complete_analysis_workflow()
    test_custom_knowledge_base()
    test_complex_scenarios()
