## tcp_flow
设备 - device_addr
ip版本 - ip_version
客户端 - client_ip_addr - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
服务器 - server_ip_addr - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
客户端端口 - client_port - 框选时间内没有建连交互，可以根据会话开始时间状态进行回查和更新来找到客户端端口。此外，对于无连接的会话，字节发收比远小于1，作为接收端的一般是客户端。
服务器端口 - server_port - 识别了服务器之后，服务器的监听端口，一般在识别未知应用的情况下（同时未采集到完整的交互数据包时）低位或靠近特权的端口的未服务器端口
客户端网段 - client_netsegment_id - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
服务器网段 - server_netsegment_id - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
客户端地理位置 - client_ip_location - 系统自带IP库中，IP所属位置
服务器地理位置 - server_ip_location - 系统自带IP库中，IP所属位置
客户端MAC地址 - client_mac - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
服务器MAC地址 - server_mac - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
应用 - application_id - 识别出的系统应用或自定义应用
虚拟网类型 - vn_type - VLAN ID/MPLS Label/VXLAN ID虚拟网类型
虚拟网标识 - vlan_id
虚拟网标识 - multi_vlan_id
接口标识 - netflow_id
虚拟网类型 - vnType - VLAN ID/MPLS Label/VXLAN ID虚拟网类型
协议 - protocol - 应用层协议（HTTP/TCP等）
时间 - time - 连接建立时间：TCP会话第一个syn包到三次握手成功的最后一个ack包的时长为连接建立时间。
客户端字节数 - client_total_byte - 客户端发送给服务器的总字节数
服务器字节数 - server_total_byte - 服务器发送给客户端的总字节数
总字节数 - total_byte - 端点间收发的数据包的总字节数
会话开始时间 - flow_start_time - TCP会话的第一个包的时间
会话结束时间 - flow_end_time - TCP会话的最后一个包的时间
会话开始时间(纳秒) - flow_start_time_ns - TCP会话的第一个包的时间
会话结束时间(纳秒) - flow_end_time_ns - TCP会话的最后一个包的时间
会话持续时间(纳秒) - flow_duration_ns - 会话结束时间 - 会话开始时间
每秒字节数 - total_byteps - 总字节数/（查询时间段时长与会话时间交集），单位为“字节/秒（Bps）”
比特率 - total_bitps - 总字节数x8/（查询时间段时长与会话时间交集），单位为“位/秒（bps）”
客户端比特率 - client_bitps - 客户端字节数x8/（查询时间段时长与会话时间交集），单位为“位/秒（bps）”
服务器比特率 - server_bitps - 服务器字节数x8/（查询时间段时长与会话时间交集），单位为“位/秒（bps）”
客户端字节数（有效载荷） - client_tcp_effective_payload_byte - 客户端发送给服务器的有效负荷总字节数
服务器字节数（有效载荷） - server_tcp_effective_payload_byte - 算法：服务器发送给客户端的有效负荷总字节数
TCP字节数（有效载荷） - total_tcp_effective_payload_byte - 客户端与服务器的所有存在的有效负荷，即有负载的数据包的总字节数。
比特率（有效载荷） - total_tcp_effective_payload_bitps - 端点间所有存在的有效负荷，即有负载。
客户端比特率（有效载荷） - client_tcp_effective_payload_bitps - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
服务器比特率（有效载荷） - server_tcp_effective_payload_bitps - 服务器字节数（有效载荷）*8/（查询时间段时长与会话时间交集）
客户端数据包数 - client_total_packet - 客户端发送数据包数
服务器数据包数 - server_total_packet - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
总数据包 - total_packet - 初级
客户端每秒数据包数 - client_total_packetps - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
服务器每秒数据包数 - server_total_packetps - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
每秒数据包数 - total_packetps - 总数据包数/（查询时间段时长与会话时间交集），单位为“包/秒（pps）”
客户端累计字节数 - client_cumulative_byte - 算法：从TCP会话开始时间到框选结束时间客户端发送给服务器的总字节数
服务器累计字节数 - server_cumulative_byte - 算法：从TCP会话开始时间到框选结束时间服务器发送给客户端的总字节数
累计字节数 - total_cumulative_byte - 算法：从TCP会话开始时间到框选结束时间客户端和服务器之间收发的总字节数
客户端累计数据包数 - client_cumulative_packet - 算法：从TCP会话开始时间到框选结束时间客户端发送给服务器的总数据包数
服务器累计数据包数 - server_cumulative_packet - 算法：从TCP会话开始时间到框选结束时间服务器发送给客户端的总数据包数
累计数据包数 - total_cumulative_packet - 算法：从TCP会话开始时间到框选结束时间客户端和服务器之间收发的总数据包数
客户端负载数据包数 - client_payload_packet - 客户端发送给服务器的带TCP负载的数据包数
服务器负载数据包数 - server_payload_packet - 服务器发送给客户端的带TCP负载的数据包数
总数据包数（有效载荷） - total_payload_packet - 初级
客户端累计负载数据包数 - client_cumulative_payload_packet - 算法：从TCP会话开始时间到框选结束时间客户端发送给服务器的带TCP负载的数据包数
服务器累计负载数据包数 - server_cumulative_payload_packet - 算法：从TCP会话开始时间到框选结束时间服务器发送给客户端的带TCP负载的数据包数
平均包长 - avg_pkt_size - 总字节数/总数据包数，单位为“字节”
客户端RTT - client_rtt - 客户端RTT：三次握手包中synack包与ack包的时间差
服务器RTT - server_rtt - 服务器RTT：三次握手包中synack包与syn包的时间差
连接建立时间 - establish_rtt - 连接建立时间：TCP会话第一个syn包到三次握手成功的最后一个ack包的时长为连接建立时间
会话持续时间 - flow_duration - 会话结束时间 - 会话开始时间
状态 - tcp_status - TCP会话状态：
TCP同步包 - tcp_syn_packet - 客户端TCP同步包 + 服务器TCP同步包
TCP同步重传包 - tcp_syn_retrans_packet
TCP同步确认包 - tcp_synack_packet - CP Flags设置了SYN ACK位的包
TCP同步确认重传包 - tcp_synack_retrans_packet
客户端TCP重置包 - client_tcp_rst_packet - TCP Flags设置了RST位的包
服务器TCP重置包 - server_tcp_rst_packet - TCP Flags设置了RST位的包
客户端TCP结束包 - client_tcp_fin_packet - TCP Flags设置了FIN位的包
服务器TCP结束包 - server_tcp_fin_packet - TCP Flags设置了FIN位的包
客户端TCP重传包 - client_tcp_retransmission_packet - 客户端发送到服务器的TCP重传包数
服务器TCP重传包 - server_tcp_retransmission_packet - 服务器发送到客户端的TCP重传包数
客户端TCP重传率 - client_tcp_retransmission_rate - 算法：客户端TCP重传包/客户端负载数据包数
服务器TCP重传率 - server_tcp_retransmission_rate - 算法：服务器TCP重传包/服务器负载数据包数
客户端TCP分段丢失包 - client_tcp_segment_lost_packet - 客户端发送到服务器的分段丢失包。
服务器TCP分段丢失包 - server_tcp_segment_lost_packet - 服务器发送到客户端的分段丢失包。
TCP分段丢失包 - tcp_segment_lost_packet - 1.分段丢失要判断2个地方，1是要判断一堆序列包中是否有缺失，2是判断后面是否有重传包（sequence比最后一个包的小，IPID比最后一个包的大）；
客户端TCP分段丢失率 - client_tcp_segment_lost_packet_rate - 算法：客户端tcp分段丢失包 / 客户端tcp带负载数据包数
服务器TCP分段丢失率 - server_tcp_segment_lost_packet_rate - 算法：服务器tcp分段丢失包 / 服务器tcp带负载数据包数
TCP分段丢失率 - tcp_segment_lost_packet_rate - TCP分段丢失包数/带负载的TCP数据包数*100%
客户端最小ACK时延 - client_min_ack_delay - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
客户端最大ACK时延 - client_max_ack_delay - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
客户端平均ACK时延 - client_avg_ack_delay - 客户端ACK时延：指的是一个客户端不带负载的ACK包与其确认的服务器带负载的数据包时间差
服务器最小ACK时延 - server_min_ack_delay - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
服务器最大ACK时延 - server_max_ack_delay - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
服务器平均ACK时延 - server_avg_ack_delay - 服务器ACK时延：指的是一个服务器不带负载的ACK包与其确认的客户端带负载的数据包时间差
连接请求总数 - tcp_shakehands_total_count - 连接请求总数 = 连接失败次数 + 三次握手次数
连接建立客户端重置次数 - client_connection_rst - 一个TCP会话的服务器发送了TCP同步确认包后，如果客户端立即返回RST包，则判定为连接请求被重置，统计一次连接请求被重置。该应用下所有TCP会话连接请求被重置次数之和即“连接请求被重置次数”
连接建立服务器重置次数 - server_connection_rst - 一个TCP会话的客户端发送了TCP同步包后，如果服务器立即返回RST包，则判定为连接请求被重置，统计一次连接请求被重置。该应用下所有TCP会话连接请求被重置次数之和即“连接请求被重置次数”
连接建立重置次数 - connection_rst - 连接建立客户端重置次数+连接建立服务器重置次数
连接建立客户端重置率 - client_connection_rst_rate - 连接建立客户端重置次数 / 连接请求总数
连接建立服务器重置率 - server_connection_rst_rate - 连接建立服务器重置次数 / 连接请求总数
连接建立重置率 - connection_rst_rate - 连接建立重置次数 / 连接请求总数
连接建立客户端无响应次数 - client_connection_noresponse - 连接请求无响应判定规则有两种：1. 一个TCP会话的服务器发送了TCP同步确认包后，客户端无任何包返回，服务器也不再发送任何数据包，则在该TCP会话超时后统计一次连接建立客户端无响应 2. 一个TCP会话的服务器发送了TCP同步确认包后，客户端无任何包返回，服务器再次发送TCP同步确认包，此时统计一次连接建立客户端无响应。连接建立客户端无响应次数即该应用下所有TCP会话连接建立客户端无响应次数之和。
连接建立服务器无响应次数 - server_connection_noresponse - 连接请求无响应判定规则有两种：1. 一个TCP会话的客户端发送了TCP同步包后，服务器无任何包返回，客户端也不再发送任何数据包，则在该TCP会话超时后统计一次连接建立服务器无响应 2. 一个TCP会话的客户端发送了TCP同步包后，服务器无任何包返回，客户端再次发送TCP同步包，此时统计一次连接建立服务器无响应。连接建立服务器无响应次数即该应用下所有TCP会话连接建立服务器无响应次数之和。
连接建立无响应次数 - connection_noresponse - 连接建立客户端无响应次数+连接建立服务器无响应次数
连接建立客户端无响应率 - client_connection_noresponse_rate - 连接建立客户端无响应次数 / 连接请求总数
连接建立服务器无响应率 - server_connection_noresponse_rate - 算法：连接建立服务器无响应次数 / 连接请求总数
连接无响应率 - tcp_connect_noresponse_rate
客户端TCP窗口为0次数 - client_tcp_window_0 - 客户端发送到服务器的TCP窗口大小为0的数据包个数
服务器TCP窗口为0次数 - server_tcp_window_0 - 服务器发送到客户端的TCP窗口大小为0的数据包个数
成功的TCP交易次数 - successful_tcp_transaction_total_count - 成功的TCP交易次数=好的TCP交易次数+一般的TCP交易次数+差的TCP交易次数+很差的TCP交易次数
很差的TCP交易次数 - tcp_transaction_worse_count - 很差的TCP交易次数：默认TCP交易响应时间在100ms以上（TCP交易响应时间可在应用配置页面设置）
最大响应时间 - tcp_transaction_max_rtt - 响应时间：第一个响应包的时间-最后一个请求包的时间；一次请求响应为单位；没带负载的ACK包不算
服务器总响应时间 - tcp_transaction_total_rtt - 服务器总响应时间
平均响应时间 - tcp_transaction_avg_rtt - 初级
TCP交易无响应次数 - tcp_transaction_no_response_count - 算法：有交易请求包，无交易响应包的次数
TCP交易响应次数 - tcp_transaction_response_count - 算法：框选时间内所有TCP交易响应总和
TCP交易请求次数 - tcp_transaction_request_count - 算法：框选时间内所有TCP交易请求总和
TCP交易总数 - tcp_transaction_total_count - 算法：MAX（TCP交易请求数或TCP交易响应次数）
TCP三次握手客户端确认包 - tcp_handshake_third_ack_packet - 算法：三次握手过程中客户端ACK数据包个数
客户端数据包数（有效载荷） - client_tcp_effective_payload_packet - 算法：客户端发送给服务器的有负载的数据包
服务器数据包数（有效载荷） - server_tcp_effective_payload_packet - 算法：服务器发送给客户端的有负载的数据包
客户端请求总传输时间 - tcp_transaction_total_request_trans_time - 客户端请求传输时间算法：交易中客户端第一个请求时间与最后一个请求时间之差+1/2 RTT（重传包不在计算范围中）；如果1个交易中只有一个请求数据包，数据传输时间等于1/2 RTT
服务器响应总传输时间 - tcp_transaction_total_response_trans_time - 服务器响应传输时间算法：交易中服务器第一个响应时间与最后一个响应时间之差+1/2 RTT（重传包不在计算范围中）；如果1个交易中只有一个响应数据包，数据传输时间等于1/2 RTT
客户端请求平均传输时间 - tcp_transaction_avg_request_trans_time - 客户端请求传输时间算法：交易中客户端第一个请求时间与最后一个请求时间之差+1/2 RTT（重传包不在计算范围中）；如果1个交易中只有一个请求数据包，数据传输时间等于1/2 RTT
服务器响应平均传输时间 - tcp_transaction_avg_response_trans_time - 服务器响应传输时间算法：交易中服务器第一个响应时间与最后一个响应时间之差+1/2 RTT（重传包不在计算范围中）；如果1个交易中只有一个响应数据包，数据传输时间等于1/2 RTT
服务器响应最大传输时间 - tcp_transaction_max_response_trans_time - 服务器响应传输时间算法：交易中服务器第一个响应时间与最后一个响应时间之差+1/2 RTT（重传包不在计算范围中）；如果1个交易中只有一个响应数据包，数据传输时间等于1/2 RTT
客户端请求最大传输时间 - tcp_transaction_max_request_trans_time - 客户端请求传输时间算法：交易中客户端第一个请求时间与最后一个请求时间之差+1/2 RTT（重传包不在计算范围中）；如果1个交易中只有一个请求数据包，数据传输时间等于1/2 RTT
第一个负载包IP标识号 - first_payload_pkt_ipid
第一个负载包TCP序列号 - first_payload_pkt_seq
客户端总重传时延 - client_tcp_total_retrans_time - 重传时延算法：如果发现重传包，收到的重传包之前的最后一个tcp序列数据包（有负载同方向），到重传包的下一个tcp序列数据包（有负载同方向）的时间间隔。如果重传包后没有tcp序列数据包（有负载同方向），则为收到的重传包之前的最后一个tcp序列数据包（有负载同方向）到最后一个重传包的时间。
客户端重传次数 - client_tcp_retrans_count - 算法：所有客户端重传包的总数
客户端平均重传时延 - client_tcp_avg_retrans_time - 重传时延算法：如果发现重传包，收到的重传包之前的最后一个tcp序列数据包（有负载同方向），到重传包的下一个tcp序列数据包（有负载同方向）的时间间隔。如果重传包后没有tcp序列数据包（有负载同方向），则为收到的重传包之前的最后一个tcp序列数据包（有负载同方向）到最后一个重传包的时间。
客户端最大重传时延 - client_tcp_max_retrans_time - 重传时延算法：如果发现重传包，收到的重传包之前的最后一个tcp序列数据包（有负载同方向重传包除外，遇到重传包继续往上找），到重传包的下一个tcp序列数据包（有负载同方向重传包除外，遇到重传包继续往下找）的时间间隔。如果重传包后没有tcp序列数据包（有负载同方向），则为收到的重传包之前的最后一个tcp序列数据包（有负载同方向）到最后一个重传包的时间。
服务器总重传时延 - server_tcp_total_retrans_time - 重传时延算法：如果发现重传包，收到的重传包之前的最后一个tcp序列数据包（有负载同方向），到重传包的下一个tcp序列数据包（有负载同方向）的时间间隔。如果重传包后没有tcp序列数据包（有负载同方向），则为收到的重传包之前的最后一个tcp序列数据包（有负载同方向）到最后一个重传包的时间。
服务器重传次数 - server_tcp_retrans_count - 算法：所有服务器重传包的总数
服务器平均重传时延 - server_tcp_avg_retrans_time - 重传时延算法：如果发现重传包，收到的重传包之前的最后一个tcp序列数据包（有负载同方向），到重传包的下一个tcp序列数据包（有负载同方向）的时间间隔。如果重传包后没有tcp序列数据包（有负载同方向），则为收到的重传包之前的最后一个tcp序列数据包（有负载同方向）到最后一个重传包的时间。
服务器最大重传时延 - server_tcp_max_retrans_time - 重传时延算法：如果发现重传包，收到的重传包之前的最后一个tcp序列数据包（有负载同方向），到重传包的下一个tcp序列数据包（有负载同方向）的时间间隔。如果重传包后没有tcp序列数据包（有负载同方向），则为收到的重传包之前的最后一个tcp序列数据包（有负载同方向）到最后一个重传包的时间。
首次响应时延 - tcp_first_res_rtt - 首次响应时延响应时延：客户端发出第一个syn到收到第一个交易带负载响应包的时间
用户总响应时间 - tcp_trade_total_res_time
平均用户响应时间 - tcp_trade_avg_res_time - 用户响应时间：TCP连接的第一个交易，连接建立时间+服务器响应时间+数据传输时间。即：第一个syn至最后一个响应报文间的间隔时间；TCP连接的第一个以后的交易，客户端传输时间+服务器响应时间+服务器传输时间。即：第一个客户端请求报文至最后一个响应报文间的间隔时间
最大用户响应时间 - tcp_trade_max_res_time - 用户响应时间：TCP连接的第一个交易，连接建立时间+服务器响应时间+数据传输时间。即：第一个syn至最后一个响应报文间的间隔时间；TCP连接的第一个以后的交易，客户端传输时间+服务器响应时间+服务器传输时间。即：第一个客户端请求报文至最后一个响应报文间的间隔时间
最大连接建立时间 - establish_max_rtt - 最大连接建立时间：连接建立时间的最大值
连接建立总时间 - establish_total_rtt
总的首次响应时延 - tcp_first_res_total_rtt - 响应时延：客户端发出第一个syn到收到第一个交易带负载响应包的时间
请求虚拟网标识 - clienttoserver_vn_id
响应虚拟网标识 - servertoclient_vn_id
请求虚拟网标识 - multi_clienttoserver_vn_id
响应虚拟网标识 - multi_servertoclient_vn_id
客户端DSCP - client_dscp - 算法：源IP为客户端的DSCP值
客户端DSCP Codepoint Name - client_dscp_name - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
服务器DSCP - server_dscp - 算法：源IP为服务器的DSCP值
服务器DSCP Codepoint Name - server_dscp_name - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
客户端TCP小窗口数 - client_small_window - 小窗口数：窗口值小于10000的数据包个数
服务器TCP小窗口数 - server_small_window - 小窗口数：窗口值小于10000的数据包个数
客户端TCP初始窗口 - client_first_tcp_window - 客户端TCP初始窗口=会话中客户端第一个窗口值
创建会话数 - create_flow_count - 只有TCP会话才有，1个3次握手过程统计为1次创建会话数。（在框选时间内新加入会话流的会话个数）
关闭会话数 - close_flow_count - 框选时间内网段间选择一个会话，在分析数据中有关闭连接的会话数
每秒创建会话数 - create_flow_countps - 创建会话数/框选时间
每秒关闭会话数 - close_flow_countps - 关闭会话数/框选时间
服务器TCP初始窗口 - server_first_tcp_window - 服务器TCP初始窗口=会话中服务器第一个窗口值
客户端TCP乱序包数 - client_tcp_disorder_packet - 客户端发送到服务器的乱序包
服务器TCP乱序包数 - server_tcp_disorder_packet - 服务器发送到客户端的乱序包
TCP乱序包数 - tcp_disorder_packet - 1、当前包Seq大于前一个包的Next Seq，说明有丢包或者乱序
客户端0窗口总时延 - total_client_0WindowRTT - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
最大客户端0窗口时延 - max_client_0WindowRTT - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
最小客户端0窗口时延 - min_client_0WindowRTT - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
平均客户端0窗口时延 - avg_client_0WindowRTT - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
服务器0窗口总时延 - total_server_0WindowRTT - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
最大服务器0窗口时延 - max_server_0WindowRTT - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
最小服务器0窗口时延 - min_server_0WindowRTT - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
平均服务器0窗口时延 - avg_server_0WindowRTT - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
客户端慢连接次数 - client_slowconnect - 三次握手连接开始到连接成功，过程中服务器发送synack包累计大于1，统计一次客户端慢连接
服务器慢连接次数 - server_slowconnect - 三次握手连接开始到连接成功，过程中客户端发送syn累计大于1，统计一次服务器慢连接
三次握手次数 - tcp_three_handshake - 三次握手的三个包必须是连续的，并且严格按照客户端发送SYN，服务器发送SYN ACK，客户端发送ACK的顺序
连接失败次数 - tcp_shakehands_failed_count - 连接建立服务器重置次数+连接建立服务器无响应次数+连接建立客户端重置次数+连接建立客户端无响应次数
连接建立成功次数 - connection_succ_count - 同一个5元组，(syn数>=1 && synack数>=1 && 客户端ack数>0)  或 (请求或应答)负载包数>0，统计为一次建连成功。
连接失败率 - tcp_connect_failure_rate - 连接失败率=TCP连接失败次数/TCP连接总次数
慢连接次数 - slowconnect - 慢连接：一个会话流，建立时，多个(>1)syn 或多个(>1)syn ack，且建连成功（连接建立成功的条件）。
慢连接占比 - slowconnect_ratio - 慢连接次数 /三次握手次数（三次握手成功次数）
连接类型 - connection_type - 连接类型
客户端目标地址(SRv6) - client_srv6_addr - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
服务器目标地址(SRv6) - server_srv6_addr - 算法： 1、采集到三次握手的会话流，客户端为传输层客户端、服务器为传输层服务器 2、未采集到三次握手，应用识别为未知应用，小端口为服务器，大端口的为客户端 3、未采集到三次握手，识别了应用，客户端为应用客户端、服务器为应用服务器
端口复用失败次数 - tcp_port_reused_failed
