#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Wireshark风格的TCP解码表达式
"""

import json
from data_preprocessor import NetworkDataPreprocessor

def test_wireshark_style_expr():
    """测试Wireshark风格的表达式"""
    print("=== 测试Wireshark风格表达式 ===")
    
    # 模拟SYN/ACK不匹配的TCP交易解码数据
    tcp_decode_data = {
        "success": True,
        "message": "TCP交易解析成功",
        "data": {
            "transactions": [
                {
                    "transaction_id": 1,
                    "retransmit": 0,
                    "server_response_time": 0,
                    "request_bytes": 0,
                    "response_bytes": 0,
                    "request_packets": 1,
                    "response_packets": 1,
                    "pkt_seq_diagram": [
                        {
                            "ack": 0,
                            "direction": 1,
                            "seq": 1000,
                            "payloadlen": 0,
                            "summaryDecoder": "<PacketSummary>[SYN]序列号=1000,确认号=0,窗口=14600,头部长度=40 字节</PacketSummary>",
                            "time": 1753163581166521888
                        },
                        {
                            "ack": 999,  # 错误的ACK，应该是1001
                            "direction": 2,
                            "seq": 2000,
                            "payloadlen": 0,
                            "summaryDecoder": "<PacketSummary>[ACK, SYN]序列号=2000,确认号=999,窗口=28400,头部长度=32 字节</PacketSummary>",
                            "time": 1753163581166524473
                        }
                    ]
                }
            ]
        }
    }
    
    preprocessor = NetworkDataPreprocessor()
    preprocessor._parse_tcp_transaction_data(tcp_decode_data)
    
    print("包序列信息:")
    for i, pkt in enumerate(preprocessor.packets):
        print(f"  包{i+1}: 方向={pkt['direction_desc']}, 标志={pkt['tcp_flags']}, seq={pkt['sequence_number']}, ack={pkt['ack_number']}")
    
    # 测试基本的包查找
    print("\n=== 测试包查找功能 ===")
    
    # 查找SYN包
    syn_pkt = preprocessor.find_pkt("tcp.flag.syn == 1 && tcp.flag.ack != 1")
    print(f"SYN包: {syn_pkt['sequence_number'] if syn_pkt else 'Not found'}")
    
    # 查找SYN+ACK包
    syn_ack_pkt = preprocessor.find_pkt("tcp.flag.syn == 1 && tcp.flag.ack == 1")
    print(f"SYN+ACK包: seq={syn_ack_pkt['sequence_number']}, ack={syn_ack_pkt['ack_number']}" if syn_ack_pkt else "Not found")
    
    # 测试完整的Wireshark风格表达式
    print("\n=== 测试完整表达式 ===")
    
    expr1 = "syn_pkt = find_pkt(tcp.flag.syn == 1 && tcp.flag.ack != 1); syn_ack_pkt = find_pkt(tcp.flag.syn == 1 && tcp.flag.ack == 1); syn_ack_pkt.ack != syn_pkt.seq + 1"
    result1 = preprocessor.evaluate_tcp_decode_expr(expr1)
    print(f"SYN/ACK不匹配检测: {result1}")
    
    # 测试其他表达式
    expr2 = "syn_pkt = find_pkt(tcp.flag.syn == 1); syn_pkt.seq > 0"
    result2 = preprocessor.evaluate_tcp_decode_expr(expr2)
    print(f"SYN包序列号大于0: {result2}")
    
    expr3 = "rst_pkt = find_pkt(tcp.flag.rst == 1); rst_pkt.seq > 0"
    result3 = preprocessor.evaluate_tcp_decode_expr(expr3)
    print(f"存在RST包: {result3}")

def test_normal_handshake():
    """测试正常握手场景"""
    print("\n\n=== 测试正常握手场景 ===")
    
    # 模拟正常握手的数据
    tcp_decode_normal = {
        "success": True,
        "message": "TCP交易解析成功",
        "data": {
            "transactions": [
                {
                    "transaction_id": 1,
                    "retransmit": 0,
                    "server_response_time": 100,
                    "request_bytes": 80,
                    "response_bytes": 200,
                    "request_packets": 1,
                    "response_packets": 1,
                    "pkt_seq_diagram": [
                        {
                            "ack": 0,
                            "direction": 1,
                            "seq": 1000,
                            "payloadlen": 0,
                            "summaryDecoder": "<PacketSummary>[SYN]序列号=1000,确认号=0,窗口=14600,头部长度=40 字节</PacketSummary>",
                            "time": 1753163581166521888
                        },
                        {
                            "ack": 1001,  # 正确的ACK
                            "direction": 2,
                            "seq": 2000,
                            "payloadlen": 0,
                            "summaryDecoder": "<PacketSummary>[ACK, SYN]序列号=2000,确认号=1001,窗口=28400,头部长度=32 字节</PacketSummary>",
                            "time": 1753163581166524473
                        },
                        {
                            "ack": 2001,
                            "direction": 1,
                            "seq": 1001,
                            "payloadlen": 80,
                            "summaryDecoder": "<PacketSummary>[ACK]序列号=1001,确认号=2001,窗口=14600,头部长度=20 字节</PacketSummary>",
                            "time": 1753163581166525000
                        }
                    ]
                }
            ]
        }
    }
    
    preprocessor = NetworkDataPreprocessor()
    preprocessor._parse_tcp_transaction_data(tcp_decode_normal)
    
    print("正常握手包序列:")
    for i, pkt in enumerate(preprocessor.packets):
        print(f"  包{i+1}: 方向={pkt['direction_desc']}, 标志={pkt['tcp_flags']}, seq={pkt['sequence_number']}, ack={pkt['ack_number']}, len={pkt['payload_length']}")
    
    # 测试SYN/ACK匹配
    expr1 = "syn_pkt = find_pkt(tcp.flag.syn == 1 && tcp.flag.ack != 1); syn_ack_pkt = find_pkt(tcp.flag.syn == 1 && tcp.flag.ack == 1); syn_ack_pkt.ack != syn_pkt.seq + 1"
    result1 = preprocessor.evaluate_tcp_decode_expr(expr1)
    print(f"SYN/ACK不匹配检测: {result1} (应该是False)")
    
    # 测试是否有数据传输
    expr2 = "data_pkt = find_pkt(tcp.len > 0); data_pkt.len > 0"
    result2 = preprocessor.evaluate_tcp_decode_expr(expr2)
    print(f"存在数据传输: {result2}")
    
    # 测试三次握手完成
    expr3 = "syn_pkt = find_pkt(tcp.flag.syn == 1 && tcp.flag.ack != 1); syn_ack_pkt = find_pkt(tcp.flag.syn == 1 && tcp.flag.ack == 1); ack_pkt = find_pkt(tcp.flag.ack == 1 && tcp.flag.syn != 1); ack_pkt.seq == syn_pkt.seq + 1"
    result3 = preprocessor.evaluate_tcp_decode_expr(expr3)
    print(f"三次握手完成: {result3}")

def test_complex_expressions():
    """测试复杂表达式"""
    print("\n\n=== 测试复杂表达式 ===")
    
    # 创建一个包含多种包类型的场景
    tcp_complex_data = {
        "success": True,
        "message": "TCP交易解析成功",
        "data": {
            "transactions": [
                {
                    "transaction_id": 1,
                    "retransmit": 1,
                    "server_response_time": 0,
                    "request_bytes": 0,
                    "response_bytes": 0,
                    "request_packets": 3,
                    "response_packets": 1,
                    "pkt_seq_diagram": [
                        {
                            "ack": 0,
                            "direction": 1,
                            "seq": 1000,
                            "payloadlen": 0,
                            "summaryDecoder": "<PacketSummary>[SYN]序列号=1000,确认号=0,窗口=14600,头部长度=40 字节</PacketSummary>",
                            "time": 1753163581166521888
                        },
                        {
                            "ack": 0,
                            "direction": 1,
                            "seq": 1000,  # 重传的SYN
                            "payloadlen": 0,
                            "summaryDecoder": "<PacketSummary>[SYN]序列号=1000,确认号=0,窗口=14600,头部长度=40 字节</PacketSummary>",
                            "time": 1753163581166522000
                        },
                        {
                            "ack": 1000,
                            "direction": 2,
                            "seq": 0,
                            "payloadlen": 0,
                            "summaryDecoder": "<PacketSummary>[RST]序列号=0,确认号=1000,窗口=0,头部长度=20 字节</PacketSummary>",
                            "time": 1753163581166523000
                        }
                    ]
                }
            ]
        }
    }
    
    preprocessor = NetworkDataPreprocessor()
    preprocessor._parse_tcp_transaction_data(tcp_complex_data)
    
    print("复杂场景包序列:")
    for i, pkt in enumerate(preprocessor.packets):
        print(f"  包{i+1}: 方向={pkt['direction_desc']}, 标志={pkt['tcp_flags']}, seq={pkt['sequence_number']}, ack={pkt['ack_number']}")
    
    # 测试SYN重传检测
    syn_pkts = preprocessor.find_pkts("tcp.flag.syn == 1")
    print(f"\nSYN包数量: {len(syn_pkts)}")
    
    # 测试RST包检测
    expr1 = "rst_pkt = find_pkt(tcp.flag.rst == 1); rst_pkt.seq >= 0"
    result1 = preprocessor.evaluate_tcp_decode_expr(expr1)
    print(f"存在RST包: {result1}")
    
    # 测试重传检测（通过相同序列号）
    expr2 = "has_retransmission == 1"
    result2 = preprocessor.evaluate_tcp_decode_expr(expr2)
    print(f"检测到重传: {result2}")

if __name__ == "__main__":
    test_wireshark_style_expr()
    test_normal_handshake()
    test_complex_expressions()
