#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试过滤器说明功能
"""

import sys
import os
import json

# 添加当前目录到Python路径
sys.path.insert(0, '/data/git/dify-mcp-client')
os.chdir('/data/git/dify-mcp-client')

def test_filter_function():
    """测试过滤器说明功能"""
    try:
        # 直接定义过滤器说明函数，避免导入复杂依赖
        def get_supported_filters() -> str:
            """
            获取支持的过滤器对象和使用说明 - 基于10.1下载过滤器文档

            Returns:
                过滤器说明的JSON字符串，包含所有支持的过滤器对象
            """
            filter_info = {
                "description": "下载过滤器支持逻辑运算符，可以对需要下载的数据包进行过滤",
                "download_filters": {
                    "ip_filters": {
                        "ip_addr": {
                            "description": "IP地址",
                            "example": "ip_addr=***********",
                            "note": "指定单个IP地址进行过滤"
                        },
                        "client_ip_addr": {
                            "description": "客户端IP地址",
                            "example": "client_ip_addr=***********",
                            "note": "在过滤时不会进行客户端IP地址的判断，过滤结果与ip_addr一样"
                        },
                        "server_ip_addr": {
                            "description": "服务端IP地址",
                            "example": "server_ip_addr=***********",
                            "note": "在过滤时不会进行服务器IP地址判断，过滤结果与ip_addr一样"
                        },
                        "ip_range": {
                            "description": "IP范围",
                            "example": "ip_range=***********-*************",
                            "note": "指定IP地址范围进行过滤"
                        },
                        "ip_flow": {
                            "description": "IP会话",
                            "example": "ip_flow=[***********]-[*********]",
                            "note": "指定两个IP地址之间的会话流"
                        }
                    },
                    "port_filters": {
                        "port": {
                            "description": "端口",
                            "example": "port=443",
                            "note": "指定单个端口进行过滤"
                        },
                        "ipport_flow": {
                            "description": "TCP/UDP会话",
                            "example": "ipport_flow=[*************]:5652-[*************]:443",
                            "note": "指定完整的IP和端口会话流"
                        }
                    },
                    "physical_filters": {
                        "phys_addr": {
                            "description": "物理地址",
                            "example": "phys_addr=1C:6F:65:97:20:66",
                            "note": "指定MAC物理地址进行过滤"
                        }
                    }
                },
                "query_filters": {
                    "description": "查询过滤器支持字段比较和逻辑运算，用于统计表查询",
                    "operators": {
                        "=": "等于",
                        "!=": "不等于",
                        ">=": "大于等于",
                        "<=": "小于等于",
                        ">": "大于",
                        "<": "小于"
                    },
                    "logical_operators": {
                        "&&": "逻辑与（需要URL编码为%26%26）",
                        "||": "逻辑或"
                    },
                    "examples": [
                        "total_byte>1000000",
                        "server_port=80",
                        "total_packet>1000",
                        "visit_count>10",
                        "total_byte>1000000 && server_port=443",
                        "server_port=80 || server_port=443"
                    ]
                }
            }

            return json.dumps(filter_info, ensure_ascii=False, indent=2)
        
        # 测试过滤器说明功能
        result = get_supported_filters()
        parsed = json.loads(result)
        
        print('✅ 过滤器说明功能测试成功!')
        print(f'- 总体描述: {parsed["description"]}')
        print(f'- 下载过滤器类别数: {len(parsed["download_filters"])}')
        print(f'- IP过滤器数量: {len(parsed["download_filters"]["ip_filters"])}')
        print(f'- 端口过滤器数量: {len(parsed["download_filters"]["port_filters"])}')
        print(f'- 查询过滤器示例数: {len(parsed["query_filters"]["examples"])}')
        
        # 显示一些具体的过滤器示例
        print('\n📋 部分过滤器示例:')
        for category, filters in list(parsed['download_filters'].items())[:3]:
            print(f'  {category}:')
            for filter_name, filter_info in list(filters.items())[:2]:
                print(f'    - {filter_name}: {filter_info["example"]}')
        
        # 显示查询过滤器示例
        print('\n🔍 查询过滤器示例:')
        for example in parsed['query_filters']['examples'][:3]:
            print(f'  - {example}')
            
        return True
        
    except Exception as e:
        print(f'❌ 测试失败: {str(e)}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_filter_function()
