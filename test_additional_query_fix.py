#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的LLM提示词是否能正确生成包含"在线TCP交易解码"的additional_query
"""

import asyncio
import json
from packet_drop_hypergraph_rag import openai_complete_if_cache

async def test_llm_with_improved_prompt():
    """测试改进后的提示词"""
    
    # 读取改进后的提示词模板
    with open('llm_prompt_template.md', 'r', encoding='utf-8') as f:
        prompt_template = f.read()
    
    # 测试用例：用户只提供了连接失败率，应该生成包含TCP交易解码的additional_query
    test_user_input = """
客户端: ************* 客户端端口: 40276 服务器: ************* 服务器端口: 80
会话开始时间: 2025/07/22 13:53:01
会话结束时间: 2025/07/22 13:53:02

查询TCP会话：连接失败率 = 100%
"""
    
    # 构建完整的提示词
    full_prompt = f"""
{prompt_template}

现在请分析以下用户输入：

{test_user_input}

请严格按照上述规则和示例，生成正确的JSON输出。特别注意：
1. 必须检查所有知识库场景的查询类型
2. 如果任何场景需要"在线TCP交易解码"，必须在additional_query中包含
3. 确保每种查询类型都有对应的查询项

请输出JSON格式的分析结果：
"""
    
    print("=== 测试改进后的提示词 ===")
    print("用户输入:")
    print(test_user_input)
    print("\n正在调用LLM...")
    
    try:
        # 调用LLM
        response = await openai_complete_if_cache(
            prompt=full_prompt,
            system_prompt="你是一个专业的网络事件分析专家。请严格按照提示词中的规则和示例进行分析，确保不遗漏任何查询类型。",
            model="deepseek-ai/DeepSeek-R1-0528",
            temperature=0.1,
            response_format={"type": "json_object"}
        )
        
        print("\nLLM响应:")
        print(response)
        
        # 解析JSON响应
        try:
            result = json.loads(response)
            
            # 检查是否包含additional_query
            if "additional_query" in result:
                additional_queries = result["additional_query"]
                print(f"\n=== 分析结果 ===")
                print(f"additional_query数量: {len(additional_queries)}")
                
                # 检查是否包含两种查询类型
                has_tcp_session_query = False
                has_tcp_decode_query = False
                
                for query_item in additional_queries:
                    query_text = query_item.get("query", "")
                    print(f"- {query_text}")
                    
                    if "查询TCP会话" in query_text:
                        has_tcp_session_query = True
                    if "在线TCP交易解码" in query_text:
                        has_tcp_decode_query = True
                
                print(f"\n=== 检查结果 ===")
                print(f"包含'查询TCP会话': {has_tcp_session_query}")
                print(f"包含'在线TCP交易解码': {has_tcp_decode_query}")
                
                if has_tcp_session_query and has_tcp_decode_query:
                    print("✅ 测试通过：LLM正确生成了两种查询类型")
                else:
                    print("❌ 测试失败：LLM遗漏了某种查询类型")
                    if not has_tcp_decode_query:
                        print("   特别是：遗漏了'在线TCP交易解码'")
            else:
                print("❌ 测试失败：响应中没有additional_query字段")
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            print("原始响应:", response[:500])
            
    except Exception as e:
        print(f"❌ LLM调用失败: {e}")

async def test_multiple_scenarios():
    """测试多个场景"""
    
    test_cases = [
        {
            "name": "场景1：只有连接失败率",
            "input": """
客户端: ************* 客户端端口: 40276 服务器: ************* 服务器端口: 80
会话开始时间: 2025/07/22 13:53:01
会话结束时间: 2025/07/22 13:53:02

查询TCP会话：连接失败率 = 100%
""",
            "expected_queries": ["查询TCP会话", "在线TCP交易解码"]
        },
        {
            "name": "场景2：部分TCP会话数据",
            "input": """
客户端: ************* 客户端端口: 8080 服务器: ************* 服务器端口: 443
会话开始时间: 2025/07/22 14:00:00
会话结束时间: 2025/07/22 14:00:05

查询TCP会话：TCP同步包 = 3, 连接建立成功次数 = 0
""",
            "expected_queries": ["查询TCP会话", "在线TCP交易解码"]
        }
    ]
    
    print("=== 多场景测试 ===")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- {test_case['name']} ---")
        
        # 这里可以添加具体的测试逻辑
        # 为了简化，我们只打印测试用例信息
        print(f"输入: {test_case['input'].strip()}")
        print(f"期望包含的查询类型: {test_case['expected_queries']}")

if __name__ == "__main__":
    print("开始测试LLM提示词修复效果...")
    
    # 运行主要测试
    asyncio.run(test_llm_with_improved_prompt())
    
    # 运行多场景测试
    asyncio.run(test_multiple_scenarios())
    
    print("\n=== 测试完成 ===")
    print("如果测试通过，说明提示词修复有效")
    print("如果测试失败，可能需要进一步调整提示词")
