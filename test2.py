import json
import re

def main(text: str) -> dict:
    # 处理多层转义
    processed_text = text.replace('\\n', '\n').replace('\\"', '"')
    
    # 多种JSON匹配模式
    patterns = [
        r'```json\s*\n(.*?)\n```',  # 标准markdown json块
        r'```json\n(.*?)\n```',     # 处理后的json块
        r'```json(.*?)```',         # 无换行的json块
        r'({.*})',                  # 任何大括号包围的内容（贪婪匹配）
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, processed_text, re.DOTALL)

        for match in matches:
            try:
                # 清理匹配的内容
                json_str = match.strip()
                # 替换非断行空格为普通空格
                json_str = json_str.replace('\u00a0', ' ')
                # 替换其他可能的空白字符
                json_str = json_str.replace('\xa0', ' ')

                #parsed_json = json.loads(json_str)
                return {
                    "result": json_str,
                    "error": "",
                    "found": True
                }
            except json.JSONDecodeError:
                continue
    
    return {
        "result":  "",
        "found": False,
        "error": "No valid JSON found"
    }

str1 = "```json\n{\n  \"status\": \"需要二次查询\",\n  \"full_matches\": [],\n  \"partial_matches\": [\"NET-AVAIL_CONNECT-FAILED_3\"],\n  \"need_more_info\": [\"NET-AVAIL_CONNECT-FAILED_1\", \"NET-AVAIL_CONNECT-FAILED_4\", \"NET-AVAIL_CONNECT-FAILED_5A\", \"NET-AVAIL_CONNECT-FAILED_5B\", \"NET-AVAIL_CONNECT-FAILED_6\"],\n  \"unmatched\": [],\n  \"additional_query\": [\n    {\n      \"query\": \"查询TCP会话: 连接建立服务器重置次数, 连接建立服务器重置率, TCP同步包, TCP同步重传包, 服务器TCP重置包, 服务器数据包数, 连接建立成功次数, 三次握手次数, 服务器TCP窗口为0次数, TCP交易无响应次数\"\n    }\n  ]\n}\n```"

print(main(str1))