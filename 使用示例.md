# 网络事件分析知识库改造 - 使用示例

## 改造完成的功能

### 1. 知识库确认条件改为Expr格式
原来的中文描述已改为程序化的表达式，支持自动判断：

**改造前：**
```
确认结果: TCP同步包 > 0 AND TCP同步重传包 > 0 AND 服务器数据包数 == 0 AND 连接建立成功次数 == 0 则满足条件
```

**改造后：**
```
确认结果: tcp_syn_packet > 0 && tcp_syn_retrans_packet > 0 && server_total_packet == 0 && connection_succ_count == 0
```

### 2. 支持中英文指标名称
系统现在支持用户输入中文或英文指标名称，会自动进行映射转换。

### 3. 完全匹配和部分匹配功能
- **完全匹配(full_matches)**: 用户提供的指标完全满足某个知识库的条件
- **部分匹配(partial_matches)**: 用户提供了部分指标，但还缺少一些字段
- **需要更多信息(need_more_info)**: 用户提供的指标与某个知识库完全不匹配，需要查询该知识库的所有字段
- **不匹配(unmatched)**: 用户提供的指标满足字段要求但不满足条件判断

### 4. 缺失字段统计
系统会详细统计每个知识库场景缺失的字段，并生成补充查询建议。

## 使用示例

### 示例1：用户输入案例

**用户输入：**
```
链路id:2 服务器：192.168.163.209 
客户端: 10.127.104.10 客户端端口: 40276 服务器: 60.28.208.186 服务器端口: 80 
会话开始时间: 2025/07/22 13:53:01 
会话结束时间: 2025/07/22 13:53:02 
查询TCP会话: tcp_connect_failure_rate = 100%
```

**系统分析结果：**
```json
{
  "status": "需要二次查询",
  "full_matches": [],
  "partial_matches": [],
  "need_more_info": [
    "NET-AVAIL_CONNECT-FAILED_1",
    "NET-AVAIL_CONNECT-FAILED_2", 
    "NET-AVAIL_CONNECT-FAILED_3",
    "NET-AVAIL_CONNECT-FAILED_4",
    "NET-AVAIL_CONNECT-FAILED_5A",
    "NET-AVAIL_CONNECT-FAILED_5B",
    "NET-AVAIL_CONNECT-FAILED_6"
  ],
  "additional_query": [
    {
      "query": "查询TCP会话: 连接建立成功次数, 连接建立服务器无响应率, 连接建立服务器重置率, 连接建立服务器重置次数, TCP同步重传包, 服务器TCP窗口为0次数, 三次握手次数, TCP交易无响应次数, 服务器数据包数, 服务器TCP重置包, TCP同步包"
    },
    {
      "query": "在线TCP交易解码: 解码该会话前10个包，时间范围为会话创建时间前2s，limit10个包"
    }
  ]
}
```

### 示例2：补充指标后的分析

**补充指标：**
```python
tcp_metrics = {
    'tcp_connect_failure_rate': 100,
    'tcp_syn_packet': 5,
    'tcp_syn_retrans_packet': 3,
    'server_total_packet': 0,
    'connection_succ_count': 0,
    'server_connection_noresponse_rate': 100
}
```

**分析结果：**
```json
{
  "status": "需要二次查询",
  "full_matches": [
    "NET-AVAIL_CONNECT-FAILED_1",
    "NET-AVAIL_CONNECT-FAILED_5A"
  ],
  "partial_matches": [
    "NET-AVAIL_CONNECT-FAILED_4",
    "NET-AVAIL_CONNECT-FAILED_5B",
    "NET-AVAIL_CONNECT-FAILED_6"
  ],
  "need_more_info": [
    "NET-AVAIL_CONNECT-FAILED_2",
    "NET-AVAIL_CONNECT-FAILED_3"
  ]
}
```

## 代码使用方法

### 基本使用
```python
from data_preprocessor import analyze_tcp_session_with_knowledge_base

# 用户输入
user_input = """
链路id:2 服务器：192.168.163.209 
客户端: 10.127.104.10 客户端端口: 40276 服务器: 60.28.208.186 服务器端口: 80 
会话开始时间: 2025/07/22 13:53:01 
会话结束时间: 2025/07/22 13:53:02 
查询TCP会话: tcp_connect_failure_rate = 100%
"""

# TCP指标数据（支持中英文字段名）
tcp_metrics = {
    'TCP同步包': 5,
    'TCP同步重传包': 3,
    '服务器数据包数': 0,
    '连接建立成功次数': 0
}

# 分析
result = analyze_tcp_session_with_knowledge_base(user_input, tcp_metrics)

# 查看结果
print(f"状态: {result['status']}")
print(f"完全匹配: {result['full_matches']}")
print(f"补充查询: {result['additional_query']}")
```

### 表达式评估
```python
from data_preprocessor import NetworkDataPreprocessor

preprocessor = NetworkDataPreprocessor()

# 测试表达式评估
metrics = {
    'tcp_syn_packet': {'value': 5},
    'server_total_packet': {'value': 0}
}

expr = "tcp_syn_packet > 0 && server_total_packet == 0"
result = preprocessor.evaluate_expr(expr, metrics)
print(f"表达式结果: {result}")  # True
```

## 支持的表达式语法

- 比较运算符：`==`, `!=`, `>`, `<`, `>=`, `<=`
- 逻辑运算符：`&&` (AND), `||` (OR)
- 括号：`()` 用于分组
- 数值：整数和浮点数

## 知识库结构

每个知识库条目包含：
- `id`: 知识库唯一标识
- `category`: 分类描述
- `query_type`: 查询类型（"查询TCP会话" 或 "在线TCP交易解码"）
- `required_fields`: 必需的字段列表
- `condition_expr`: 条件表达式（Expr格式）
- `decode_params`: 解码参数（仅适用于TCP交易解码）

## 输出字段说明

- `status`: "完全匹配" 或 "需要二次查询"
- `full_matches`: 完全匹配的知识库ID列表
- `partial_matches`: 部分匹配的知识库ID列表  
- `need_more_info`: 需要更多信息的知识库ID列表
- `unmatched`: 不匹配的知识库ID列表
- `additional_query`: 补充查询建议
- `missing_fields_detail`: 各知识库缺失字段详情
- `meta_info`: 从用户输入提取的会话元信息
