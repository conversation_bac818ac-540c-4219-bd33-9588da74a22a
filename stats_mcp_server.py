#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于FastMCP的统计数据查询服务
提供统计表查询工具，支持指定时间范围、字段、过滤条件等参数
"""

import json
import traceback
import urllib.request
import time
import struct
import ssl
import os
from typing import List, Dict, Any, Optional
from datetime import datetime
from fastmcp import FastMCP

# 创建FastMCP服务实例
mcp = FastMCP("统计数据查询服务")

class StatsApiClient:
    def __init__(self, url: str, user: str, password: str, version: str = "6.1"):
        """
        初始化统计API客户端
        Args:
            url: API服务器URL
            user: 用户名
            password: 密码
            version: API版本
        """
        self.url = url
        self.user = user
        self.password = password
        self.ver = version
        self.session = ''
        
    def strtime_to_inttime(self, strtime: str) -> int:
        """字符串时间转换为时间戳"""
        tupletime = time.strptime(strtime, '%Y-%m-%d %H:%M:%S')
        itime = time.mktime(tupletime)
        return int(itime)
        
    def login(self) -> bool:
        """登录API"""
        if self.ver >= '5.4':
            page = "csras_api/login"
        else:
            page = "login.php"
            
        param = {"username": self.user, "password": self.password}
        try:
            resdata = self.request_data(page, param)
            js_obj = json.loads(resdata)
            if js_obj['login_errcode'] == 0:
                self.session = js_obj['session']
                return True
            else:
                return False
        except Exception:
            return False
        
    def logout(self):
        """登出"""
        if not self.session:
            return
            
        if self.ver >= '5.4':
            page = f"csras_api/{self.session}/logout/"
        else:
            page = f"logout.php?session={self.session}"
            
        try:
            self.request_data(page, '')
        except:
            pass

    def request_data(self, page: str, param: Any) -> bytes:
        """发送HTTP请求"""
        request_url = self.url + page
        if param:
            if isinstance(param, dict):
                param = json.dumps(param, sort_keys=False)
            param = 'param=' + urllib.parse.quote(param)
            req = urllib.request.Request(request_url, param.encode('utf-8'))
        else:
            req = urllib.request.Request(request_url, ''.encode('utf-8'))
        
        response = urllib.request.urlopen(req)
        return response.read()

    def get_api_data_status(self, data: bytes) -> int:
        """获取API响应状态码"""
        if data[0:1] == '{'.encode():
            try:
                js_obj = json.loads(data)
                return js_obj.get('errcode', -1)
            except Exception:
                return -1
        else:
            strcode = data[0:2]
            rndata = struct.unpack("!H", strcode)
            return rndata[0]

    def query_stats_data(self, table: str, begintime: str, endtime: str, 
                        fields: List[str], keys: List[str], timeunit: int = 0,
                        filter_condition: str = "", topcount: int = 1000,
                        sortfield: str = "total_byte", sorttype: int = 2,
                        netlink: int = 2, keycount: Optional[int] = None,
                        fieldcount: Optional[int] = None) -> Dict[str, Any]:
        """
        查询统计数据
        Args:
            table: 统计表名
            begintime: 开始时间
            endtime: 结束时间
            fields: 查询字段列表
            keys: 键字段列表
            timeunit: 时间单位
            filter_condition: 过滤条件
            topcount: 返回记录数
            sortfield: 排序字段
            sorttype: 排序类型
            netlink: 网络链路
            keycount: 键字段数量 (可选，通常设为None)
            fieldcount: 查询字段数量 (可选，通常设为None)
        """
        # 构建查询参数
        param = {
            "netlink": netlink,
            "table": table,
            "keys": keys,
            "fields": fields,
            "sorttype": sorttype,
            "sortfield": sortfield,
            "filter": filter_condition,
            "topcount": topcount,
            "keycount": keycount,
            "fieldcount": fieldcount,
            "begintime": self.strtime_to_inttime(begintime) * 1000,
            "endtime": self.strtime_to_inttime(endtime) * 1000,
            "timeunit": timeunit
        }
        
        # 执行查询
        if self.ver >= '5.4':
            page = f"csras_api/{self.session}/stats_data"
        else:
            page = f"statsquery.php?session={self.session}"
            
        resdata = self.request_data(page, param)
        
        # 检查响应状态
        return_code = self.get_api_data_status(resdata)
        
        if return_code != 0:
            return {
                "success": False,
                "error_code": return_code,
                "message": f"查询失败，错误码: {return_code}",
                "data": None
            }
        
        # 解析二进制数据为CSV格式
        try:
            # 首先尝试标准的二进制解析
            csv_data = self.parse_binary_to_csv(resdata, keys + fields)
            
            # 如果标准解析返回错误信息，尝试智能响应解析
            if csv_data.startswith("# 二进制解析错误") or csv_data.startswith("# 数据解析错误"):
                # 将二进制数据转换为字符串并尝试智能解析
                data_str = resdata.decode('utf-8', errors='ignore')
                csv_data = self.parse_response_data(data_str, keys + fields)
            
            return {
                "success": True,
                "error_code": 0,
                "message": "查询成功",
                "data": csv_data,
                "raw_data_size": len(resdata)
            }
        except Exception as e:
            return {
                "success": False,
                "error_code": -1,
                "message": f"数据解析失败: {str(e)}",
                "data": None,
                "raw_data_size": len(resdata)
            }

    def parse_binary_to_csv(self, data: bytes, columns: List[str]) -> str:
        """
        解析二进制数据为CSV格式 - 基于parse_api_data.py的完整实现
        Args:
            data: 二进制响应数据
            columns: 列名列表
        Returns:
            CSV格式字符串
        """
        try:
            offset = 0
            
            # 读取返回流中的错误码 (2字节)
            if len(data) < 2:
                return ",".join(columns)
            
            ret_code = struct.unpack("!H", data[offset:offset+2])[0]
            offset += 2
            
            if ret_code != 0:
                return f"# API错误码: {ret_code}\n" + ",".join(columns)
            
            # 读取错误信息的长度和错误信息内容 (4字节长度 + 内容)
            if len(data) < offset + 4:
                return ",".join(columns)
            
            err_msg_len = struct.unpack("!I", data[offset:offset+4])[0]
            offset += 4
            
            if len(data) < offset + err_msg_len:
                return ",".join(columns)
            
            err_msg = data[offset:offset+err_msg_len].decode('utf-8', errors='ignore')
            offset += err_msg_len
            
            # 解析所有表字段 - 字段数量 (2字节)
            if len(data) < offset + 2:
                return ",".join(columns)
            
            field_count = struct.unpack("!H", data[offset:offset+2])[0]
            offset += 2
            
            if field_count <= 0:
                return ",".join(columns)
            
            # 读取字段信息
            field_list = []
            for i in range(field_count):
                if len(data) < offset + 4:
                    break
                
                # 字段名长度 (4字节)
                name_len = struct.unpack("!I", data[offset:offset+4])[0]
                offset += 4
                
                if len(data) < offset + name_len + 1:
                    break
                
                # 字段名 (name_len字节)
                field_name = data[offset:offset+name_len].decode('utf-8', errors='ignore').strip('\x00')
                offset += name_len
                
                # 字段类型 (1字节)
                field_type = struct.unpack("!B", data[offset:offset+1])[0]
                offset += 1
                
                field_list.append({'name': field_name, 'type': field_type})
            
            # 解析链路数量 (1字节)
            if len(data) < offset + 1:
                return ",".join(columns)
            
            link_count = struct.unpack("!B", data[offset:offset+1])[0]
            offset += 1
            
            # 解析链路ID (2字节)
            if len(data) < offset + 2:
                return ",".join(columns)
            
            link_id = struct.unpack("!H", data[offset:offset+2])[0]
            offset += 2
            
            # 时间集个数 (4字节)
            if len(data) < offset + 4:
                return ",".join(columns)
            
            bucket_count = struct.unpack("!I", data[offset:offset+4])[0]
            offset += 4
            
            # 构建CSV数据
            csv_lines = []
            # 使用解析到的字段名作为标题
            field_names = [field['name'] for field in field_list]
            csv_lines.append(",".join(field_names))
            
            # 循环处理链路时间集
            for bucket_idx in range(bucket_count):
                if len(data) < offset + 8:
                    break
                
                # 解析链路记录集时间 (8字节时间戳)
                ticks = struct.unpack("!Q", data[offset:offset+8])[0]
                offset += 8
                str_time = self._convert_ticks_to_time(ticks / 1000)
                
                if len(data) < offset + 4:
                    break
                
                # 记录条数 (4字节)
                record_num = struct.unpack("!I", data[offset:offset+4])[0]
                offset += 4
                
                # 处理每条记录
                for rec_idx in range(record_num):
                    if offset >= len(data):
                        break
                    
                    row = []
                    
                    # 遍历每个字段，进行取值
                    for field in field_list:
                        try:
                            value = self._parse_field_by_type(data, offset, field['type'])
                            if isinstance(value, tuple):
                                field_value, new_offset = value
                                offset = new_offset
                            else:
                                field_value = value
                            
                            # 对于时间字段，使用解析到的时间
                            if field['name'].lower() == 'time' and field['type'] == 6:
                                row.append(str_time)
                            else:
                                row.append(str(field_value))
                                
                        except Exception as e:
                            row.append("")
                            break
                    
                    if len(row) == len(field_list) and any(row):
                        csv_lines.append(",".join(row))
            
            return "\n".join(csv_lines)
            
        except Exception as e:
            # 如果解析失败，尝试混合数据解析
            try:
                data_str = data.decode('utf-8', errors='ignore')
                if ',' in data_str:
                    return self.parse_mixed_data_to_csv(data_str, columns)
            except:
                pass
                
            error_info = f"# 二进制解析错误: {str(e)}\n"
            error_info += f"# 原始数据大小: {len(data)} 字节\n"
            error_info += f"# 数据预览: {data[:50].hex()}\n"
            return error_info + ",".join(columns)
    
    def _convert_ticks_to_time(self, ticks):
        """将时间戳转换为时间字符串"""
        try:
            return time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(ticks))
        except:
            return str(int(ticks))
    
    def _parse_field_by_type(self, data: bytes, offset: int, field_type: int):
        """根据字段类型解析数据"""
        try:
            # UINT8，1字节的无符号整数
            if field_type == 1:
                if offset + 1 > len(data):
                    return "", offset
                val = struct.unpack("!B", data[offset:offset+1])[0]
                return val, offset + 1
            
            # UINT16，2字节的无符号整数
            elif field_type == 2:
                if offset + 2 > len(data):
                    return "", offset
                val = struct.unpack("!H", data[offset:offset+2])[0]
                return val, offset + 2
            
            # UINT32，4字节的无符号整数
            elif field_type == 3:
                if offset + 4 > len(data):
                    return "", offset
                val = struct.unpack("!I", data[offset:offset+4])[0]
                return val, offset + 4
            
            # UINT64，8字节的无符号整数
            elif field_type == 4:
                if offset + 8 > len(data):
                    return "", offset
                val = struct.unpack("!Q", data[offset:offset+8])[0]
                return val, offset + 8
            
            # DOUBLE，8字节的浮点数
            elif field_type == 5:
                if offset + 8 > len(data):
                    return "", offset
                val = struct.unpack("!d", data[offset:offset+8])[0]
                return f"{val:.6f}", offset + 8
            
            # DATETIME，8字节的时间戳
            elif field_type == 6:
                if offset + 8 > len(data):
                    return "", offset
                ticks = struct.unpack("!Q", data[offset:offset+8])[0]
                str_time = self._convert_ticks_to_time(ticks / 1000)
                return str_time, offset + 8
            
            # TEXT，变长的文本字符串: length[4] + value[n]
            elif field_type == 7:
                if offset + 4 > len(data):
                    return "", offset
                text_len = struct.unpack("!I", data[offset:offset+4])[0]
                offset += 4
                if offset + text_len > len(data):
                    return "", offset
                text_val = data[offset:offset+text_len].decode('utf-8', errors='ignore')
                return text_val, offset + text_len
            
            # PERCENT，百分值，长度4字节
            elif field_type == 8:
                if offset + 4 > len(data):
                    return "", offset
                val = struct.unpack("!I", data[offset:offset+4])[0]
                percent = float(val) / 100
                return f"{percent}%", offset + 4
            
            # MAC，MAC地址，8个字节
            elif field_type == 9:
                if offset + 8 > len(data):
                    return "", offset
                mac_bytes = data[offset:offset+8]
                mac_str = ":".join([f"{b:02x}" for b in mac_bytes[:6]])
                return mac_str, offset + 8
            
            # IPADDR，由1字节的IP版本+n字节的IP地址组成
            elif field_type == 10:
                if offset + 1 > len(data):
                    return "", offset
                ip_ver = struct.unpack("!B", data[offset:offset+1])[0]
                offset += 1
                
                if ip_ver == 4:
                    if offset + 4 > len(data):
                        return "", offset
                    ip_bytes = data[offset:offset+4]
                    ip_str = ".".join([str(b) for b in ip_bytes])
                    return ip_str, offset + 4
                elif ip_ver == 6:
                    if offset + 16 > len(data):
                        return "", offset
                    ip_bytes = data[offset:offset+16]
                    # 简化IPv6显示
                    ip_str = ":".join([f"{ip_bytes[i]:02x}{ip_bytes[i+1]:02x}" for i in range(0, 16, 2)])
                    return ip_str, offset + 16
                else:
                    return "N/A", offset
            
            # 变长的请求或响应内容：length[4] + value[n]
            elif field_type == 11:
                if offset + 4 > len(data):
                    return "", offset
                content_len = struct.unpack("!I", data[offset:offset+4])[0]
                offset += 4
                if offset + content_len > len(data):
                    return "", offset
                content = data[offset:offset+content_len].decode('utf-8', errors='ignore')
                return content, offset + content_len
            
            # 虚拟ID, 如vlan id，1字节无符号整数
            elif field_type == 12:
                if offset + 1 > len(data):
                    return "", offset
                val = struct.unpack("!B", data[offset:offset+1])[0]
                return val, offset + 1
            
            else:
                return "", offset
                
        except Exception:
            return "", offset

    def parse_mixed_data_to_csv(self, raw_data: str, columns: List[str]) -> str:
        """
        专门处理API返回的混合格式数据（CSV头部+二进制数据）
        Args:
            raw_data: API返回的原始字符串数据
            columns: 期望的列名列表
        Returns:
            清理后的CSV格式字符串
        """
        try:
            # 清理数据：移除null字符和不可打印字符
            cleaned_data = ''.join(c if c.isprintable() or c in '\n\r\t,' else ' ' for c in raw_data)
            
            # 按行分割
            lines = cleaned_data.split('\n')
            csv_lines = []
            
            # 使用提供的列名作为标题
            csv_lines.append(",".join(columns))
            
            # 查找并处理数据行
            data_started = False
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                    
                # 跳过注释行和损坏的行
                if line.startswith('#') or line.startswith('"otal_bitps'):
                    continue
                
                # 检查是否是有效的数据行
                if ',' in line and not line.startswith('time,time'):
                    # 分割并清理字段
                    fields = [field.strip().replace('"', '') for field in line.split(',')]
                    
                    # 确保字段数量与列数匹配
                    if len(fields) >= len(columns):
                        fields = fields[:len(columns)]
                    elif len(fields) < len(columns):
                        fields.extend([''] * (len(columns) - len(fields)))
                    
                    # 验证和清理字段值
                    cleaned_fields = []
                    for i, field in enumerate(fields):
                        if field and field != 'total_byte':  # 避免重复的列名
                            # 尝试识别数值
                            try:
                                if '.' in field:
                                    float(field)
                                else:
                                    int(field)
                                cleaned_fields.append(field)
                            except:
                                # 如果不是数值，保留原值（可能是时间戳等）
                                cleaned_fields.append(field)
                        else:
                            cleaned_fields.append('')
                    
                    if any(cleaned_fields):  # 只添加非空行
                        csv_lines.append(",".join(cleaned_fields))
            
            return "\n".join(csv_lines)
            
        except Exception as e:
            return f"# 混合数据解析错误: {str(e)}\n" + ",".join(columns)

    def parse_response_data(self, raw_data: str, columns: List[str]) -> str:
        """
        智能解析响应数据 - 处理各种可能的数据格式
        Args:
            raw_data: API返回的原始字符串数据
            columns: 期望的列名列表
        Returns:
            清理后的CSV格式字符串
        """
        try:
            # 检查是否是空查询结果（以连续逗号结尾）
            if raw_data.endswith(',,,,,,,') or raw_data.endswith(',,,,,,,,'):
                # 返回只有标题行的CSV
                return ",".join(columns)
            
            # 检查是否包含CSV格式的数据
            if ',' in raw_data:
                # 按行分割数据
                lines = raw_data.split('\n')
                csv_lines = []
                
                # 使用提供的列名作为标题
                csv_lines.append(",".join(columns))
                
                # 查找有效的数据行
                for line in lines:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    
                    # 跳过损坏的行或标题行
                    if (line.startswith('time,time') or 
                        line.startswith('"otal_bitps') or
                        line == 'total_byte'):
                        continue
                    
                    # 检查是否是有效的数据行（包含逗号但不全是逗号）
                    if ',' in line and not line.replace(',', '').strip() == '':
                        # 清理字段
                        fields = [field.strip().replace('"', '').replace('\x00', '') 
                                for field in line.split(',')]
                        
                        # 过滤掉空的或无效的字段
                        if any(field for field in fields if field and field != ''):
                            # 确保字段数量匹配
                            while len(fields) < len(columns):
                                fields.append('')
                            if len(fields) > len(columns):
                                fields = fields[:len(columns)]
                            
                            csv_lines.append(",".join(fields))
                
                return "\n".join(csv_lines)
            
            # 如果没有逗号，可能是纯文本错误信息
            return f"# 数据格式错误: {raw_data[:100]}...\n" + ",".join(columns)
            
        except Exception as e:
            return f"# 响应解析错误: {str(e)}\n" + ",".join(columns)

# 全局API客户端实例
api_client = None

@mcp.tool()
def setup_api_connection(url: str) -> str:
    """
    设置API连接参数 (测试版本 - 无需登录)
    
    Args:
        url: API服务器URL (例如: https://192.168.163.209:8080/)
    
    Returns:
        连接设置结果
    """
    global api_client
    try:
        # 忽略SSL证书验证
        ssl._create_default_https_context = ssl._create_unverified_context
        
        # 创建测试客户端实例，使用默认测试账号
        api_client = StatsApiClient(url, "test_user", "test_pass", "6.1")
        # 跳过登录验证，直接设置为已连接状态
        api_client.session = "test_session_12345"
        
        return f"API连接设置成功 (测试模式)，连接到 {url}"
    except Exception as e:
        return f"API连接设置失败: {str(e)}"

@mcp.tool()
def query_statistics_table(
    table: str,
    begintime: str,
    endtime: str,
    fields: List[str],
    keys: List[str],
    timeunit: int = 0,
    filter_condition: str = "",
    topcount: int = 1000,
    sortfield: str = "total_byte",
    sorttype: int = 2,
    netlink: int = 2,
    keycount: Optional[int] = None,
    fieldcount: Optional[int] = None
) -> str:
    """
    查询统计表数据
    
    Args:
        table: 统计表名 (例如: service_access, ip_flow, app_flow等)
        begintime: 开始时间 (格式: YYYY-MM-DD HH:MM:SS)
        endtime: 结束时间 (格式: YYYY-MM-DD HH:MM:SS)
        fields: 查询字段列表 (例如: ["server_ip_addr", "total_byte", "protocol"])
        keys: 键字段列表 (例如: ["server_ip_addr", "server_port"])
        timeunit: 时间单位 (0=不按照时间维度合并, 1000=秒级+key, 10000=10s级+key, 60000=分钟级+key, 3600000=小时级+key, 86400000=天级+key)
        filter_condition: 过滤条件 (例如: "total_byte>1000")
        topcount: 返回记录数 (默认: 1000)
        sortfield: 排序字段 (默认: total_byte)
        sorttype: 排序类型 (1=升序, 2=降序)
        netlink: 网络链路ID (默认: 2)
        keycount: 键字段数量 (可选，通常设为None)
        fieldcount: 查询字段数量 (可选，通常设为None)
    
    Returns:
        查询结果JSON字符串
    """
    global api_client
    
    if api_client is None:
        return json.dumps({
            "success": False,
            "message": "请先使用 setup_api_connection 设置API连接"
        }, ensure_ascii=False, indent=2)
    
    try:
        result = api_client.query_stats_data(
            table=table,
            begintime=begintime,
            endtime=endtime,
            fields=fields,
            keys=keys,
            timeunit=timeunit,
            filter_condition=filter_condition,
            topcount=topcount,
            sortfield=sortfield,
            sorttype=sorttype,
            netlink=netlink,
            keycount=keycount,
            fieldcount=fieldcount
        )
        
        # 添加查询参数信息
        result["query_info"] = {
            "table": table,
            "time_range": f"{begintime} 到 {endtime}",
            "fields_count": len(fields),
            "keys_count": len(keys),
            "filter": filter_condition or "无",
            "topcount": topcount,
            "keycount": keycount,
            "fieldcount": fieldcount
        }
        
        return json.dumps(result, ensure_ascii=False, indent=2)
        
    except Exception as e:
        return json.dumps({
            "success": False,
            "message": f"查询失败: {str(e)}",
            "traceback": traceback.format_exc()
        }, ensure_ascii=False, indent=2)

@mcp.tool()
def get_common_table_configs() -> str:
    """
    获取常用统计表的配置示例
    
    Returns:
        常用表配置的JSON字符串
    """
    common_configs = {
        "service_access": {
            "description": "服务访问统计表",
            "common_keys": ["server_ip_addr", "server_port", "client_ip_addr"],
            "common_fields": [
                "server_ip_addr", "server_port", "client_ip_addr", "device_addr",
                "total_byte", "protocol", "ip_protocol", "total_packet",
                "total_byteps", "total_bitps", "total_packetps"
            ],
            "recommended_sort": "total_byte",
            "filter_examples": ["total_byte>1000", "server_port=80", "total_packet>100"],
            "note": "filter_examples 提供 filter_condition 参数的使用示例"
        },
        "ip_flow": {
            "description": "IP流统计表",
            "common_keys": ["ip_endpoint1", "ip_endpoint2"],
            "common_fields": [
                "ip_endpoint1", "ip_endpoint2", "protocol", "total_byte",
                "total_byteps", "total_bitps", "total_packet", "total_packetps",
                "avg_pkt_size", "tcp_syn_packet", "tcp_synack_packet",
                "endpoint1_tx_byte", "endpoint2_tx_byte", "connection_rst"
            ],
            "recommended_sort": "total_byte",
            "filter_examples": ["total_byte>1000000", "total_packet>1000", "avg_pkt_size>64"]
        },
        "tcp_flow": {
            "description": "TCP流统计表",
            "common_keys": ["client_ip_addr", "server_ip_addr", "client_port", "server_port"],
            "common_fields": [
                "client_ip_addr", "server_ip_addr", "client_port", "server_port",
                "total_byte", "total_packet", "flow_duration", "tcp_status",
                "client_total_byte", "server_total_byte", "avg_pkt_size"
            ],
            "recommended_sort": "total_byte",
            "filter_examples": ["flow_duration>1000", "total_byte>100000", "total_packet>100"]
        },
        "tcp_server_port": {
            "description": "TCP服务端口统计表",
            "common_keys": ["server_ip_addr", "server_port", "application_id", "protocol"],
            "common_fields": [
                "server_ip_addr", "server_port", "application_id", "protocol",
                "total_byte", "total_packet", "visit_count", "total_byteps",
                "total_bitps", "client_total_byte", "server_total_byte"
            ],
            "recommended_sort": "total_byte",
            "filter_examples": ["visit_count>10", "server_port=80", "total_byte>1000000"]
        },
        "application": {
            "description": "应用统计表",
            "common_keys": ["application_id"],
            "common_fields": [
                "application_id", "application_name", "uplink_byte", "downlink_byte",
                "total_byte", "total_byteps", "uplink_packet", "downlink_packet",
                "total_packet", "total_packetps", "create_flow_count", "alive_flow_count"
            ],
            "recommended_sort": "total_byte",
            "filter_examples": ["total_byte>1000000", "alive_flow_count>5", "create_flow_count>10"]
        },
        "internal_ip_addr": {
            "description": "内部IP地址统计表",
            "common_keys": ["ip_addr"],
            "common_fields": [
                "ip_addr", "netsegment_id", "tx_byte", "rx_byte", "total_byte",
                "total_byteps", "tx_packet", "rx_packet", "total_packet",
                "total_packetps", "create_flow_count", "alive_flow_count"
            ],
            "recommended_sort": "total_byte",
            "filter_examples": ["total_byte>1000000", "alive_flow_count>10", "total_packet>1000"]
        },
        "external_ip_addr": {
            "description": "外部IP地址统计表",
            "common_keys": ["ip_addr"],
            "common_fields": [
                "ip_addr", "tx_byte", "rx_byte", "total_byte", "total_packet",
                "tx_packet", "rx_packet", "create_flow_count", "alive_flow_count"
            ],
            "recommended_sort": "total_byte",
            "filter_examples": ["total_byte>500000", "create_flow_count>5", "total_packet>500"]
        },
        "netsegment": {
            "description": "网段统计表",
            "common_keys": ["netsegment_id"],
            "common_fields": [
                "netsegment_id", "tx_byte", "rx_byte", "total_byte", "total_byteps",
                "tx_packet", "rx_packet", "total_packet", "total_packetps",
                "total_utilization", "avg_pkt_size"
            ],
            "recommended_sort": "total_byte",
            "filter_examples": ["total_byte>1000000", "total_utilization>50", "avg_pkt_size>64"]
        },
        "netsegment_flow": {
            "description": "网段流统计表", 
            "common_keys": ["netsegment_id1", "netsegment_id2", "application_id", "protocol"],
            "common_fields": [
                "netsegment_id1", "netsegment_id2", "application_id", "protocol",
                "endpoint1_tx_byte", "endpoint2_tx_byte", "total_byte", "total_byteps",
                "total_packet", "total_packetps", "avg_pkt_size"
            ],
            "recommended_sort": "total_byte",
            "filter_examples": ["total_byte>100000", "total_packet>100", "avg_pkt_size>64"]
        },
        "summary": {
            "description": "汇总统计表",
            "common_keys": ["time"],
            "common_fields": [
                "time", "total_byte", "total_byteps", "total_bitps",
                "inbound_byte", "inbound_byteps", "outbound_byte", "outbound_byteps",
                "total_packet", "total_packetps", "total_utilization"
            ],
            "recommended_sort": "total_byte",
            "filter_examples": ["total_utilization>50", "total_byte>1000000", "total_packet>10000"]
        }
    }
    
    return json.dumps(common_configs, ensure_ascii=False, indent=2)

@mcp.tool()
def disconnect_api() -> str:
    """
    断开API连接
    
    Returns:
        断开连接结果
    """
    global api_client
    
    if api_client is None:
        return "没有活动的API连接"
    
    try:
        api_client.logout()
        api_client = None
        return "API连接已断开"
    except Exception as e:
        return f"断开连接时出错: {str(e)}"

@mcp.tool()
def get_time_examples() -> str:
    """
    获取时间格式和timeunit参数的说明
    
    Returns:
        时间参数说明
    """
    time_info = {
        "time_format": {
            "format": "YYYY-MM-DD HH:MM:SS",
            "examples": [
                "2025-06-01 00:00:00",
                "2025-06-03 23:59:59",
                "2025-01-15 14:30:00"
            ]
        },
        "timeunit_values": {
            "0": "不按照时间维度合并，即为查询时间按照key进行合并统计",
            "1000": "按照秒级和Key组合统计", 
            "10000": "按照10s级和Key组合统计",
            "60000": "按照分钟级和Key组合统计",
            "3600000": "按照小时级和Key组合统计",
            "86400000": "按照天级和Key组合统计"
        },
        "filter_examples": [
            "total_byte>1000000",
            "server_port=80",
            "total_packet>1000",
            "visit_count>10"
        ],
        "sorttype_values": {
            "1": "升序排列",
            "2": "降序排列"
        }
    }
    
    return json.dumps(time_info, ensure_ascii=False, indent=2)

@mcp.tool()
def get_supported_filters() -> str:
    """
    获取支持的过滤器对象和使用说明 - 基于10.1下载过滤器文档

    Returns:
        过滤器说明的JSON字符串，包含所有支持的过滤器对象
    """
    filter_info = {
        "description": "下载过滤器支持逻辑运算符，可以对需要下载的数据包进行过滤",
        "download_filters": {
            "ip_filters": {
                "ip_addr": {
                    "description": "IP地址",
                    "example": "ip_addr=***********",
                    "note": "指定单个IP地址进行过滤"
                },
                "client_ip_addr": {
                    "description": "客户端IP地址",
                    "example": "client_ip_addr=***********",
                    "note": "在过滤时不会进行客户端IP地址的判断，过滤结果与ip_addr一样"
                },
                "server_ip_addr": {
                    "description": "服务端IP地址",
                    "example": "server_ip_addr=***********",
                    "note": "在过滤时不会进行服务器IP地址判断，过滤结果与ip_addr一样"
                },
                "ip_range": {
                    "description": "IP范围",
                    "example": "ip_range=***********-*************",
                    "note": "指定IP地址范围进行过滤"
                },
                "ip_flow": {
                    "description": "IP会话",
                    "example": "ip_flow=[***********]-[*********]",
                    "note": "指定两个IP地址之间的会话流"
                }
            },
            "port_filters": {
                "port": {
                    "description": "端口",
                    "example": "port=443",
                    "note": "指定单个端口进行过滤"
                },
                "client_port": {
                    "description": "客户端端口",
                    "example": "client_port=8080",
                    "note": "在过滤时不会进行客户端端口的判断，过滤结果与port一样"
                },
                "server_port": {
                    "description": "服务端端口",
                    "example": "server_port=80",
                    "note": "在过滤时不会进行服务器端口的判断，过滤结果与port一样"
                },
                "port_range": {
                    "description": "端口范围",
                    "example": "port_range=80-443",
                    "note": "指定端口范围进行过滤"
                },
                "ipport_flow": {
                    "description": "TCP/UDP会话",
                    "example": "ipport_flow=[*************]:5652-[*************]:443",
                    "note": "指定完整的IP和端口会话流"
                }
            },
            "physical_filters": {
                "phys_addr": {
                    "description": "物理地址",
                    "example": "phys_addr=1C:6F:65:97:20:66",
                    "note": "指定MAC物理地址进行过滤"
                },
                "phys_range": {
                    "description": "物理地址范围",
                    "example": "phys_range=FF:FF:FF:FF:FF:00-FF:FF:FF:FF:FF:FF",
                    "note": "指定MAC地址范围进行过滤"
                },
                "phys_flow": {
                    "description": "物理会话",
                    "example": "phys_flow=[90:2B:34:12:26:EE]-[33:33:00:01:00:03]",
                    "note": "指定两个MAC地址之间的会话流"
                }
            },
            "application_filters": {
                "application": {
                    "description": "应用",
                    "example": "application=30001",
                    "note": "只支持'='操作符，应用ID通过配置获取接口获取"
                },
                "netsegment": {
                    "description": "网段",
                    "example": "netsegment=30002",
                    "note": "只支持'='操作符，网段ID通过配置获取接口获取"
                },
                "netsegment_flow": {
                    "description": "网段会话",
                    "example": "netsegment_flow=[30002]-[65101]",
                    "note": "指定两个网段之间的会话流"
                }
            },
            "protocol_filters": {
                "tree_protocol": {
                    "description": "协议树协议",
                    "example": "tree_protocol=245",
                    "note": "通过数据包的所有协议标签过滤，协议ID可以通过网络协议信息查询获取"
                },
                "top_protocol": {
                    "description": "顶层协议",
                    "example": "top_protocol=443",
                    "note": "数据包所属的顶层协议"
                }
            },
            "vlan_mpls_filters": {
                "vlan_id": {
                    "description": "VLAN ID",
                    "example": "vlan_id=236",
                    "note": "指定VLAN标识符进行过滤"
                },
                "vlan_id_range": {
                    "description": "VLAN ID范围",
                    "example": "vlan_id_range=1-236",
                    "note": "指定VLAN ID范围进行过滤"
                },
                "mplsvpn_id": {
                    "description": "MPLS VPN标签",
                    "example": "mplsvpn_id=768",
                    "note": "指定MPLS VPN标签进行过滤"
                },
                "mplsvpn_range": {
                    "description": "MPLS VPN标签范围",
                    "example": "mplsvpn_range=1-768",
                    "note": "指定MPLS VPN标签范围进行过滤"
                }
            },
            "interface_filters": {
                "netflow_id": {
                    "description": "Netflow接口ID",
                    "example": "netflow_id=1",
                    "note": "指定Netflow接口标识符"
                },
                "netflow_id_range": {
                    "description": "Netflow接口ID范围",
                    "example": "netflow_id_range=1-10",
                    "note": "指定Netflow接口ID范围"
                },
                "adapter_id": {
                    "description": "网卡ID",
                    "example": "adapter_id=1",
                    "note": "网卡ID通过配置获取接口获取"
                },
                "adapter_id_range": {
                    "description": "网卡ID范围",
                    "example": "adapter_id_range=1-3",
                    "note": "指定网卡ID范围进行过滤"
                }
            },
            "qos_filters": {
                "dscp": {
                    "description": "DSCP值",
                    "example": "dscp=1",
                    "note": "指定DSCP（Differentiated Services Code Point）值"
                },
                "dscp_range": {
                    "description": "DSCP范围",
                    "example": "dscp_range=1-10",
                    "note": "指定DSCP值范围进行过滤"
                }
            }
        },
        "query_filters": {
            "description": "查询过滤器支持字段比较和逻辑运算，用于统计表查询",
            "operators": {
                "=": "等于",
                "!=": "不等于",
                ">=": "大于等于",
                "<=": "小于等于",
                ">": "大于",
                "<": "小于"
            },
            "logical_operators": {
                "&&": "逻辑与（需要URL编码为%26%26）",
                "||": "逻辑或"
            },
            "examples": [
                "total_byte>1000000",
                "server_port=80",
                "total_packet>1000",
                "visit_count>10",
                "total_byte>1000000 && server_port=443",
                "server_port=80 || server_port=443"
            ],
            "url_encoding_notes": {
                "&": "在URL中需要转义为%26",
                "&&": "逻辑与在URL中需要转义为%26%26"
            }
        },
        "usage_notes": {
            "logical_operations": "下载过滤器支持逻辑运算符，可以组合多个过滤条件",
            "id_acquisition": "应用ID、网段ID、网卡ID等需要通过配置获取接口获取",
            "protocol_ids": "协议ID可以通过网络协议信息查询接口获取",
            "string_values": "过滤条件中的字符串值不需要添加引号"
        }
    }

    return json.dumps(filter_info, ensure_ascii=False, indent=2)

if __name__ == '__main__':
    mcp.run()