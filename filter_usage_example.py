#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
过滤器使用示例
演示如何使用新增的get_supported_filters功能
"""

import json

def demo_filter_usage():
    """演示过滤器使用方法"""
    
    # 模拟调用get_supported_filters()的返回结果
    filter_info = {
        "description": "下载过滤器支持逻辑运算符，可以对需要下载的数据包进行过滤",
        "download_filters": {
            "ip_filters": {
                "ip_addr": {
                    "description": "IP地址",
                    "example": "ip_addr=***********",
                    "note": "指定单个IP地址进行过滤"
                },
                "ip_range": {
                    "description": "IP范围",
                    "example": "ip_range=***********-*************",
                    "note": "指定IP地址范围进行过滤"
                },
                "ip_flow": {
                    "description": "IP会话",
                    "example": "ip_flow=[***********]-[*********]",
                    "note": "指定两个IP地址之间的会话流"
                }
            },
            "port_filters": {
                "port": {
                    "description": "端口",
                    "example": "port=443",
                    "note": "指定单个端口进行过滤"
                },
                "ipport_flow": {
                    "description": "TCP/UDP会话",
                    "example": "ipport_flow=[*************]:5652-[*************]:443",
                    "note": "指定完整的IP和端口会话流"
                }
            }
        },
        "query_filters": {
            "description": "查询过滤器支持字段比较和逻辑运算，用于统计表查询",
            "operators": {
                "=": "等于",
                ">": "大于",
                ">=": "大于等于",
                "<": "小于",
                "<=": "小于等于",
                "!=": "不等于"
            },
            "logical_operators": {
                "&&": "逻辑与（需要URL编码为%26%26）",
                "||": "逻辑或"
            },
            "examples": [
                "total_byte>1000000",
                "server_port=80",
                "total_byte>1000000 && server_port=443",
                "server_port=80 || server_port=443"
            ]
        }
    }
    
    print("🔍 过滤器使用示例")
    print("=" * 50)
    
    print(f"\n📖 {filter_info['description']}")
    
    print("\n🌐 下载过滤器示例:")
    print("-" * 30)
    
    # 显示IP过滤器示例
    print("IP过滤器:")
    for name, info in filter_info['download_filters']['ip_filters'].items():
        print(f"  • {name}: {info['example']}")
        print(f"    说明: {info['note']}")
    
    print("\n端口过滤器:")
    for name, info in filter_info['download_filters']['port_filters'].items():
        print(f"  • {name}: {info['example']}")
        print(f"    说明: {info['note']}")
    
    print("\n📊 查询过滤器示例:")
    print("-" * 30)
    
    print("支持的操作符:")
    for op, desc in filter_info['query_filters']['operators'].items():
        print(f"  • {op}: {desc}")
    
    print("\n逻辑操作符:")
    for op, desc in filter_info['query_filters']['logical_operators'].items():
        print(f"  • {op}: {desc}")
    
    print("\n实际使用示例:")
    for example in filter_info['query_filters']['examples']:
        print(f"  • {example}")
    
    print("\n💡 实际应用场景:")
    print("-" * 30)
    
    scenarios = [
        {
            "场景": "监控特定服务器的流量",
            "过滤器": "ip_addr=***********00",
            "说明": "只下载来自或发往***********00的数据包"
        },
        {
            "场景": "分析HTTP/HTTPS流量",
            "过滤器": "server_port=80 || server_port=443",
            "说明": "查询端口为80或443的统计数据"
        },
        {
            "场景": "查找大流量会话",
            "过滤器": "total_byte>10000000",
            "说明": "查询传输字节数超过10MB的会话"
        },
        {
            "场景": "特定网段间的通信",
            "过滤器": "ip_flow=[***********/24]-[10.0.0.0/8]",
            "说明": "分析两个网段之间的通信流量"
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"{i}. {scenario['场景']}")
        print(f"   过滤器: {scenario['过滤器']}")
        print(f"   说明: {scenario['说明']}")
        print()

if __name__ == '__main__':
    demo_filter_usage()
