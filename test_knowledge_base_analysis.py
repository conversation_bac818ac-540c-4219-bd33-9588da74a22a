#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试知识库分析功能
"""

import json
from data_preprocessor import analyze_tcp_session_with_knowledge_base, NetworkDataPreprocessor

def test_full_match_scenario():
    """测试完全匹配场景"""
    print("=== 测试完全匹配场景 ===")
    
    user_input = """
    链路id:2 服务器：192.168.163.209 
    客户端: 10.127.104.10 客户端端口: 40276 服务器: 60.28.208.186 服务器端口: 80 
    会话开始时间: 2025/07/22 13:53:01 
    会话结束时间: 2025/07/22 13:53:02 
    查询TCP会话: tcp_connect_failure_rate = 100%
    """
    
    # 模拟用户提供的指标数据（对应NET-AVAIL_CONNECT-FAILED_5A）
    tcp_metrics = {
        'tcp_syn_packet': 5,
        'tcp_syn_retrans_packet': 3,
        'server_total_packet': 0,
        'connection_succ_count': 0
    }
    
    result = analyze_tcp_session_with_knowledge_base(user_input, tcp_metrics)
    print("分析结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    print()

def test_partial_match_scenario():
    """测试部分匹配场景"""
    print("=== 测试部分匹配场景 ===")
    
    user_input = """
    链路id:2 服务器：192.168.163.209 
    客户端: 10.127.104.10 客户端端口: 40276 服务器: 60.28.208.186 服务器端口: 80 
    会话开始时间: 2025/07/22 13:53:01 
    会话结束时间: 2025/07/22 13:53:02 
    查询TCP会话: tcp_connect_failure_rate = 100%
    """
    
    # 只提供部分指标
    tcp_metrics = {
        'tcp_syn_packet': 5,
        'server_total_packet': 0
        # 缺少 tcp_syn_retrans_packet 和 connection_succ_count
    }
    
    result = analyze_tcp_session_with_knowledge_base(user_input, tcp_metrics)
    print("分析结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    print()

def test_chinese_field_names():
    """测试中文字段名支持"""
    print("=== 测试中文字段名支持 ===")
    
    user_input = """
    链路id:2 服务器：192.168.163.209 
    客户端: 10.127.104.10 客户端端口: 40276 服务器: 60.28.208.186 服务器端口: 80 
    会话开始时间: 2025/07/22 13:53:01 
    会话结束时间: 2025/07/22 13:53:02 
    查询TCP会话: 连接失败率 = 100%
    """
    
    # 使用中文字段名
    tcp_metrics = {
        'TCP同步包': 5,
        'TCP同步重传包': 3,
        '服务器数据包数': 0,
        '连接建立成功次数': 0
    }
    
    result = analyze_tcp_session_with_knowledge_base(user_input, tcp_metrics)
    print("分析结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    print()

def test_need_more_info_scenario():
    """测试需要更多信息场景"""
    print("=== 测试需要更多信息场景 ===")
    
    user_input = """
    链路id:2 服务器：192.168.163.209 
    客户端: 10.127.104.10 客户端端口: 40276 服务器: 60.28.208.186 服务器端口: 80 
    会话开始时间: 2025/07/22 13:53:01 
    会话结束时间: 2025/07/22 13:53:02 
    查询TCP会话: tcp_connect_failure_rate = 100%
    """
    
    # 只提供一个不相关的指标
    tcp_metrics = {
        'tcp_connect_failure_rate': 100
    }
    
    result = analyze_tcp_session_with_knowledge_base(user_input, tcp_metrics)
    print("分析结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    print()

def test_expr_evaluation():
    """测试表达式评估功能"""
    print("=== 测试表达式评估功能 ===")
    
    preprocessor = NetworkDataPreprocessor()
    
    # 测试简单表达式
    metrics = {
        'tcp_syn_packet': {'value': 5},
        'server_total_packet': {'value': 0},
        'connection_succ_count': {'value': 0}
    }
    
    expr1 = "tcp_syn_packet > 0 && server_total_packet == 0"
    result1 = preprocessor.evaluate_expr(expr1, metrics)
    print(f"表达式: {expr1}")
    print(f"结果: {result1}")
    
    # 测试复杂表达式
    expr2 = "tcp_syn_packet > 0 && server_total_packet == 0 && connection_succ_count == 0"
    result2 = preprocessor.evaluate_expr(expr2, metrics)
    print(f"表达式: {expr2}")
    print(f"结果: {result2}")
    
    # 测试OR表达式
    metrics2 = {
        'server_tcp_window_0': {'value': 2},
        'tcp_transaction_no_response_count': {'value': 0}
    }
    expr3 = "server_tcp_window_0 > 0 || tcp_transaction_no_response_count > 0"
    result3 = preprocessor.evaluate_expr(expr3, metrics2)
    print(f"表达式: {expr3}")
    print(f"结果: {result3}")
    print()

if __name__ == "__main__":
    test_full_match_scenario()
    test_partial_match_scenario()
    test_chinese_field_names()
    test_need_more_info_scenario()
    test_expr_evaluation()
