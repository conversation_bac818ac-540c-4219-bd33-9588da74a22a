# 网络事件分析知识库

<!-- 
## 使用说明

### 知识库命名规范
- 格式：{类别}_{子类别}_{序号}
- 类别：NET-AVAIL(网络可用性)、APP-PERFORMANCE(应用性能)
- 子类别：CONNECT-FAILED(连接失败)、DATA-TRANS(数据传输)、PERFORMANCE(性能)、RESPONSE(响应)、THROUGHPUT(吞吐量)、CONNECTION(连接质量)

### 排查流程说明
1. 每个知识库都包含明确的排查过程和确认条件
2. 排查过程中涉及的指标都来自raw_field_tcpflow.md中定义的字段
3. 需要在线解码的场景会特别标注，因为涉及到包的协议内容分析
4. 确认条件使用具体的数值阈值和逻辑判断条件

### 根因分析
每个场景都提供了可能的根因分析方向，帮助运维人员快速定位问题原因并制定解决方案。 -->

## 网络可用性 - 连接失败场景

### 知识库: NET-AVAIL_CONNECT-FAILED_1
**类别：** 网络可用性/连接失败/未完成三次握手情况
**排查过程：**
1. 查询TCP会话: 连接建立服务器无响应率(server_connection_noresponse_rate), 服务器数据包数(server_total_packet)
   1.1 确认结果: server_connection_noresponse_rate == 100 && server_total_packet == 0

**根因分析指向：**
- 网络路径问题： SYN包在去往服务器的路上被丢弃（如网络拥塞、路由黑洞）
- 防火墙静默丢弃： 防火墙配置了DROP规则，它"假装没看见"SYN包，不作任何回应
- 服务器无响应： SYN包到达了服务器，但服务器的SYN/ACK响应在返回途中丢失了
- 服务器SYN队列溢出： 服务器因负载过高或遭受SYN Flood攻击，其用于处理新连接的队列已满，直接丢弃了新的SYN包

### 知识库: NET-AVAIL_CONNECT-FAILED_2
**类别：** 网络可用性/连接失败/SYN和SYNACK不匹配
**排查过程：**
1. 在线TCP交易解码: 解码该会话前10个包，时间范围为会话创建时间前2s，limit10个包
   1.1 确认结果: syn_ack_seq != syn_seq + 1

**根因分析指向：**
- 有状态网络设备故障： 路径上的负载均衡器、NAT网关或防火墙的状态表发生错乱，将不相关的会话信息串在了一起
- 非对称路由： 请求和响应走了不同的路径，导致路径上的状态设备无法正确匹配会话
- 中间人攻击 (MitM)： 有攻击者在中间伪造SYN/ACK包，企图劫持或干扰连接
- 服务器TCP协议栈Bug： 极其罕见，但理论上可能存在

### 知识库: NET-AVAIL_CONNECT-FAILED_3
**类别：** 网络可用性/连接失败/SYN包发送收到RST
**排查过程：**
1. 查询TCP会话: 连接建立服务器重置次数(server_connection_rst), 连接建立服务器重置率(server_connection_rst_rate)
   1.1 确认结果: server_connection_rst > 0 && server_connection_rst_rate > 0

**根因分析指向：**
- 服务器端口未监听： 这是最常见的原因。服务器上根本没有应用程序在等待这个端口的连接
- 防火墙策略： 中间或服务器上的防火墙配置了REJECT规则，它主动地拒绝了该连接请求，并回复了一个RST包

### 知识库: NET-AVAIL_CONNECT-FAILED_4
**类别：** 网络可用性/连接失败/服务器端口不通
**排查过程：**
1. 查询TCP会话: TCP同步包(tcp_syn_packet), 服务器TCP重置包(server_tcp_rst_packet), 连接建立成功次数(connection_succ_count)
   1.1 确认结果: tcp_syn_packet > 0 && server_tcp_rst_packet > 0 && connection_succ_count == 0

**根因分析指向：**
- 服务器端口未监听：服务器上没有应用程序在监听该端口
- 服务器操作系统内核直接回复RST包：表明这是一个网络连接层面的可用性问题

### 知识库: NET-AVAIL_CONNECT-FAILED_5A
**类别：** 网络可用性/连接失败/防火墙策略-静默丢弃
**排查过程：**
1. 查询TCP会话: TCP同步包(tcp_syn_packet), TCP同步重传包(tcp_syn_retrans_packet), 服务器数据包数(server_total_packet), 连接建立成功次数(connection_succ_count)
   1.1 确认结果: tcp_syn_packet > 0 && tcp_syn_retrans_packet > 0 && server_total_packet == 0 && connection_succ_count == 0

**根因分析指向：**
- 防火墙静默丢弃：防火墙配置了DROP规则，直接将SYN包丢弃，不作任何回应
- 网络路径问题：SYN包在传输过程中被丢弃

### 知识库: NET-AVAIL_CONNECT-FAILED_5B
**类别：** 网络可用性/连接失败/防火墙策略-拒绝
**排查过程：**
1. 查询TCP会话: TCP同步包(tcp_syn_packet), 服务器TCP重置包(server_tcp_rst_packet), 连接建立服务器重置次数(server_connection_rst)
   1.1 确认结果: tcp_syn_packet > 0 && server_tcp_rst_packet > 0 && server_connection_rst > 0

**根因分析指向：**
- 防火墙拒绝策略：防火墙配置了REJECT规则，主动回复TCP RST包或ICMP Port Unreachable消息
- 连接被直接拒绝：防火墙明确拒绝了连接请求

### 知识库: NET-AVAIL_CONNECT-FAILED_6
**类别：** 网络可用性/连接失败/服务器负载过高
**排查过程：**
1. 查询TCP会话: 三次握手次数(tcp_three_handshake), 服务器TCP窗口为0次数(server_tcp_window_0), TCP交易无响应次数(tcp_transaction_no_response_count), 连接建立成功次数(connection_succ_count)
   1.1 确认结果: tcp_three_handshake > 0 && connection_succ_count > 0 && (server_tcp_window_0 > 0 || tcp_transaction_no_response_count > 0)

**根因分析指向：**
- 服务器应用层负载过高：CPU满载、线程池耗尽等原因导致应用程序无法处理新连接
- 服务器接收窗口持续为0：暗示服务器的应用层已经无法接收更多数据
- TCP连接成功但应用层长时间无数据交互：表现为"伪连接成功"的场景

