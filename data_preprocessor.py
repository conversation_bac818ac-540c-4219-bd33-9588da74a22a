#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络事件分析数据预处理器
将复杂的JSON查询结果转换为大模型友好的格式
"""

import json
import re
from typing import List, Dict, Any, Optional, Tuple, Set
from datetime import datetime
import operator


class NetworkDataPreprocessor:
    """网络数据预处理器"""

    def __init__(self):
        self.tcp_flow_metrics = {}
        self.tcp_transaction_data = {}
        self.packet_sequence = []
        self.field_translations = self._load_field_translations()
        self.reverse_field_translations = {v: k for k, v in self.field_translations.items()}
        self.knowledge_base = self._load_knowledge_base()
    
    def preprocess(self, output_list: List[str]) -> str:
        """
        预处理网络查询结果
        
        Args:
            output_list: 包含JSON字符串的列表
            
        Returns:
            格式化的分析文本
        """
        try:
            # 解析每个输出项
            for output_item in output_list:
                self._parse_output_item(output_item)
            
            # 生成格式化的分析文本
            formatted_text = self._generate_formatted_text()
            return formatted_text
            
        except Exception as e:
            return f"数据预处理失败: {str(e)}"
    
    def _parse_output_item(self, output_item: str):
        """解析单个输出项"""
        try:
            # 首先尝试直接解析JSON
            if output_item.startswith('{"text":'):
                outer_data = json.loads(output_item)
                json_str = outer_data.get('text', '')
            else:
                json_str = output_item

            # 解析内部JSON
            data = json.loads(json_str)
            
            if not data.get('success'):
                return
            
            # 根据消息类型处理数据
            message = data.get('message', '')
            if '查询成功' in message:
                self._parse_tcp_flow_data(data)
            elif 'TCP交易解析成功' in message:
                self._parse_tcp_transaction_data(data)
                
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
        except Exception as e:
            print(f"数据解析失败: {e}")
    
    def _parse_tcp_flow_data(self, data: Dict[str, Any]):
        """解析TCP流数据"""
        csv_data = data.get('data', '')
        if not csv_data:
            return
        
        lines = csv_data.strip().split('\n')
        if len(lines) < 2:
            return
        
        # 解析表头和数据
        headers = lines[0].split(',')
        values = lines[1].split(',')
        
        # 构建指标字典
        for header, value in zip(headers, values):
            try:
                # 转换数值
                if '.' in value:
                    numeric_value = float(value)
                else:
                    numeric_value = int(value)
                
                self.tcp_flow_metrics[header.strip()] = {
                    'value': numeric_value,
                    'formatted': self._format_metric_value(header.strip(), numeric_value)
                }
            except ValueError:
                self.tcp_flow_metrics[header.strip()] = {
                    'value': value,
                    'formatted': value
                }
        
        # 保存查询信息
        self.query_info = data.get('query_info', {})
    
    def _parse_tcp_transaction_data(self, data: Dict[str, Any]):
        """解析TCP交易数据"""
        transactions = data.get('data', {}).get('transactions', [])
        if not transactions:
            return

        transaction = transactions[0]  # 取第一个交易
        self.tcp_transaction_data = transaction

        # 解析包序列图
        pkt_seq_diagram = transaction.get('pkt_seq_diagram', [])
        self.packet_sequence = []

        for pkt in pkt_seq_diagram:
            packet_info = self._parse_packet_info(pkt)
            self.packet_sequence.append(packet_info)

        # 提取TCP交易解码的关键指标用于Expr评估
        self.tcp_decode_metrics = self._extract_tcp_decode_metrics()
    
    def _parse_packet_info(self, pkt: Dict[str, Any]) -> Dict[str, Any]:
        """解析单个数据包信息"""
        # 解析summaryDecoder中的信息
        summary = pkt.get('summaryDecoder', '')
        tcp_flags = self._extract_tcp_flags(summary)
        seq_info = self._extract_sequence_info(summary)

        # 解析时间戳
        timestamp = pkt.get('time', 0)
        formatted_time = self._format_timestamp(timestamp)

        # 解析窗口大小和头部长度
        window_size = self._extract_window_size(summary)
        header_length = self._extract_header_length(summary)

        return {
            'pkt_num': pkt.get('pktNum', 0),
            'direction': pkt.get('direction'),
            'direction_desc': '客户端→服务器' if pkt.get('direction') == 1 else '服务器→客户端',
            'tcp_flags': tcp_flags,
            'sequence_number': pkt.get('seq'),
            'ack_number': pkt.get('ack'),
            'payload_length': pkt.get('payloadlen', 0),
            'timestamp': timestamp,
            'formatted_time': formatted_time,
            'window_size': window_size,
            'header_length': header_length,
            'ip_id': pkt.get('ipid', 0),
            'summary': summary,
            'seq_info': seq_info
        }
    
    def _extract_tcp_flags(self, summary: str) -> List[str]:
        """提取TCP标志位"""
        flags = []
        # 匹配方括号内的标志位
        flag_pattern = r'\[([^\]]+)\]'
        matches = re.findall(flag_pattern, summary)

        for match in matches:
            # 处理组合标志位，如 "ACK, SYN"
            individual_flags = [flag.strip() for flag in match.split(',')]
            flags.extend(individual_flags)

        return flags
    
    def _extract_sequence_info(self, summary: str) -> Dict[str, Any]:
        """提取序列号信息"""
        seq_match = re.search(r'序列号=(\d+)', summary)
        ack_match = re.search(r'确认号=(\d+)', summary)

        return {
            'seq': int(seq_match.group(1)) if seq_match else None,
            'ack': int(ack_match.group(1)) if ack_match else None
        }

    def _format_timestamp(self, timestamp: int) -> str:
        """格式化时间戳"""
        if timestamp == 0:
            return "N/A"

        # 转换纳秒时间戳为秒
        timestamp_sec = timestamp / 1_000_000_000
        try:
            dt = datetime.fromtimestamp(timestamp_sec)
            return dt.strftime("%H:%M:%S.%f")[:-3]  # 保留毫秒
        except (ValueError, OSError):
            return f"Raw:{timestamp}"

    def _extract_window_size(self, summary: str) -> int:
        """提取窗口大小"""
        window_match = re.search(r'窗口=(\d+)', summary)
        return int(window_match.group(1)) if window_match else 0

    def _extract_header_length(self, summary: str) -> int:
        """提取头部长度"""
        header_match = re.search(r'头部长度=(\d+)', summary)
        return int(header_match.group(1)) if header_match else 0
    
    def _format_metric_value(self, metric_name: str, value: float) -> str:
        """格式化指标值"""
        # 百分比指标
        if 'rate' in metric_name.lower() and value <= 1.0:
            return f"{value * 100:.1f}%"
        
        # 整数指标
        if isinstance(value, float) and value.is_integer():
            return str(int(value))
        
        return str(value)
    
    def _generate_formatted_text(self) -> str:
        """生成格式化的分析文本"""
        sections = []
        
        # 1. 会话基本信息
        if hasattr(self, 'query_info'):
            sections.append(self._format_session_info())
        
        # 2. TCP流指标
        if self.tcp_flow_metrics:
            sections.append(self._format_tcp_metrics())
        
        # 3. 在线解码结果
        if self.packet_sequence:
            sections.append(self._format_packet_sequence())
        
        return '\n\n'.join(sections)
    
    def _format_session_info(self) -> str:
        """格式化会话信息"""
        info = self.query_info
        filter_str = info.get('filter', '')
        
        # 解析过滤条件
        client_ip = self._extract_filter_value(filter_str, 'client_ip_addr')
        client_port = self._extract_filter_value(filter_str, 'client_port')
        server_ip = self._extract_filter_value(filter_str, 'server_ip_addr')
        server_port = self._extract_filter_value(filter_str, 'server_port')
        
        return f"""## 会话基本信息
- 客户端: {client_ip}:{client_port}
- 服务器: {server_ip}:{server_port}
- 时间范围: {info.get('time_range', 'N/A')}
- 数据表: {info.get('table', 'N/A')}"""
    
    def _format_tcp_metrics(self) -> str:
        """格式化TCP指标"""
        lines = ["## TCP会话指标"]

        for metric_name, metric_data in self.tcp_flow_metrics.items():
            formatted_value = metric_data['formatted']
            chinese_name = self._translate_field_name(metric_name)
            lines.append(f"- {chinese_name} == {formatted_value}")

        return '\n'.join(lines)
    
    def _format_packet_sequence(self) -> str:
        """格式化包序列"""
        lines = ["## 在线TCP交易解码结果"]

        # 计算包间时间间隔
        for i, pkt in enumerate(self.packet_sequence):
            flags_str = ', '.join(pkt['tcp_flags']) if pkt['tcp_flags'] else 'None'

            # 计算与前一个包的时间间隔
            time_interval = ""
            if i > 0:
                prev_timestamp = self.packet_sequence[i-1]['timestamp']
                curr_timestamp = pkt['timestamp']
                if prev_timestamp > 0 and curr_timestamp > 0:
                    interval_ns = curr_timestamp - prev_timestamp
                    interval_ms = interval_ns / 1_000_000  # 转换为毫秒
                    time_interval = f" (+{interval_ms:.3f}ms)"

            lines.append(f"第{i+1}个包（{pkt['direction_desc']}）：")
            lines.append(f"  时间: {pkt['formatted_time']}{time_interval}")
            lines.append(f"  标志: [{flags_str}]")
            lines.append(f"  序列号: {pkt['sequence_number']}, 确认号: {pkt['ack_number']}")
            lines.append(f"  载荷长度: {pkt['payload_length']} 字节, 窗口大小: {pkt['window_size']}")
            lines.append(f"  头部长度: {pkt['header_length']} 字节, IP ID: {pkt['ip_id']}")
            lines.append("")  # 空行分隔

        return '\n'.join(lines)
    

    
    def _extract_filter_value(self, filter_str: str, key: str) -> str:
        """从过滤条件中提取值"""
        pattern = f"{key}=([^&\\s]+)"
        match = re.search(pattern, filter_str)
        return match.group(1) if match else 'N/A'

    def _load_field_translations(self) -> Dict[str, str]:
        """加载字段翻译映射"""
        return {
            # 基本信息
            'device_addr': '设备',
            'ip_version': 'IP版本',
            'client_ip_addr': '客户端IP',
            'server_ip_addr': '服务器IP',
            'client_port': '客户端端口',
            'server_port': '服务器端口',
            'client_netsegment_id': '客户端网段',
            'server_netsegment_id': '服务器网段',
            'client_ip_location': '客户端地理位置',
            'server_ip_location': '服务器地理位置',
            'client_mac': '客户端MAC地址',
            'server_mac': '服务器MAC地址',
            'application_id': '应用',
            'protocol': '协议',
            'time': '时间',

            # 字节数相关
            'client_total_byte': '客户端字节数',
            'server_total_byte': '服务器字节数',
            'total_byte': '总字节数',
            'client_tcp_effective_payload_byte': '客户端字节数（有效载荷）',
            'server_tcp_effective_payload_byte': '服务器字节数（有效载荷）',
            'total_tcp_effective_payload_byte': 'TCP字节数（有效载荷）',

            # 时间相关
            'flow_start_time': '会话开始时间',
            'flow_end_time': '会话结束时间',
            'flow_start_time_ns': '会话开始时间(纳秒)',
            'flow_end_time_ns': '会话结束时间(纳秒)',
            'flow_duration_ns': '会话持续时间(纳秒)',
            'flow_duration': '会话持续时间',

            # 速率相关
            'total_byteps': '每秒字节数',
            'total_bitps': '比特率',
            'client_bitps': '客户端比特率',
            'server_bitps': '服务器比特率',
            'total_tcp_effective_payload_bitps': '比特率（有效载荷）',
            'client_tcp_effective_payload_bitps': '客户端比特率（有效载荷）',
            'server_tcp_effective_payload_bitps': '服务器比特率（有效载荷）',

            # 数据包相关
            'client_total_packet': '客户端数据包数',
            'server_total_packet': '服务器数据包数',
            'total_packet': '总数据包',
            'client_total_packetps': '客户端每秒数据包数',
            'server_total_packetps': '服务器每秒数据包数',
            'total_packetps': '每秒数据包数',
            'client_payload_packet': '客户端负载数据包数',
            'server_payload_packet': '服务器负载数据包数',
            'total_payload_packet': '总数据包数（有效载荷）',

            # 累计数据
            'client_cumulative_byte': '客户端累计字节数',
            'server_cumulative_byte': '服务器累计字节数',
            'total_cumulative_byte': '累计字节数',
            'client_cumulative_packet': '客户端累计数据包数',
            'server_cumulative_packet': '服务器累计数据包数',
            'total_cumulative_packet': '累计数据包数',
            'client_cumulative_payload_packet': '客户端累计负载数据包数',
            'server_cumulative_payload_packet': '服务器累计负载数据包数',

            # RTT相关
            'client_rtt': '客户端RTT',
            'server_rtt': '服务器RTT',
            'establish_rtt': '连接建立时间',
            'establish_max_rtt': '最大连接建立时间',
            'establish_total_rtt': '连接建立总时间',
            'avg_pkt_size': '平均包长',

            # TCP状态和标志
            'tcp_status': '状态',
            'tcp_syn_packet': 'TCP同步包',
            'tcp_syn_retrans_packet': 'TCP同步重传包',
            'tcp_synack_packet': 'TCP同步确认包',
            'tcp_synack_retrans_packet': 'TCP同步确认重传包',
            'client_tcp_rst_packet': '客户端TCP重置包',
            'server_tcp_rst_packet': '服务器TCP重置包',
            'client_tcp_fin_packet': '客户端TCP结束包',
            'server_tcp_fin_packet': '服务器TCP结束包',

            # 重传相关
            'client_tcp_retransmission_packet': '客户端TCP重传包',
            'server_tcp_retransmission_packet': '服务器TCP重传包',
            'client_tcp_retransmission_rate': '客户端TCP重传率',
            'server_tcp_retransmission_rate': '服务器TCP重传率',
            'client_tcp_segment_lost_packet': '客户端TCP分段丢失包',
            'server_tcp_segment_lost_packet': '服务器TCP分段丢失包',
            'tcp_segment_lost_packet': 'TCP分段丢失包',
            'client_tcp_segment_lost_packet_rate': '客户端TCP分段丢失率',
            'server_tcp_segment_lost_packet_rate': '服务器TCP分段丢失率',
            'tcp_segment_lost_packet_rate': 'TCP分段丢失率',

            # ACK时延
            'client_min_ack_delay': '客户端最小ACK时延',
            'client_max_ack_delay': '客户端最大ACK时延',
            'client_avg_ack_delay': '客户端平均ACK时延',
            'server_min_ack_delay': '服务器最小ACK时延',
            'server_max_ack_delay': '服务器最大ACK时延',
            'server_avg_ack_delay': '服务器平均ACK时延',

            # 连接相关
            'tcp_shakehands_total_count': '连接请求总数',
            'client_connection_rst': '连接建立客户端重置次数',
            'server_connection_rst': '连接建立服务器重置次数',
            'connection_rst': '连接建立重置次数',
            'client_connection_rst_rate': '连接建立客户端重置率',
            'server_connection_rst_rate': '连接建立服务器重置率',
            'connection_rst_rate': '连接建立重置率',
            'client_connection_noresponse': '连接建立客户端无响应次数',
            'server_connection_noresponse': '连接建立服务器无响应次数',
            'connection_noresponse': '连接建立无响应次数',
            'client_connection_noresponse_rate': '连接建立客户端无响应率',
            'server_connection_noresponse_rate': '连接建立服务器无响应率',
            'tcp_connect_noresponse_rate': '连接无响应率',

            # 窗口相关
            'client_tcp_window_0': '客户端TCP窗口为0次数',
            'server_tcp_window_0': '服务器TCP窗口为0次数',
            'client_small_window': '客户端TCP小窗口数',
            'server_small_window': '服务器TCP小窗口数',
            'client_first_tcp_window': '客户端TCP初始窗口',
            'server_first_tcp_window': '服务器TCP初始窗口',

            # 交易相关
            'successful_tcp_transaction_total_count': '成功的TCP交易次数',
            'tcp_transaction_worse_count': '很差的TCP交易次数',
            'tcp_transaction_max_rtt': '最大响应时间',
            'tcp_transaction_total_rtt': '服务器总响应时间',
            'tcp_transaction_avg_rtt': '平均响应时间',
            'tcp_transaction_no_response_count': 'TCP交易无响应次数',
            'tcp_transaction_response_count': 'TCP交易响应次数',
            'tcp_transaction_request_count': 'TCP交易请求次数',
            'tcp_transaction_total_count': 'TCP交易总数',

            # 握手相关
            'tcp_handshake_third_ack_packet': 'TCP三次握手客户端确认包',
            'tcp_three_handshake': '三次握手次数',
            'tcp_shakehands_failed_count': '连接失败次数',
            'connection_succ_count': '连接建立成功次数',
            'tcp_connect_failure_rate': '连接失败率',

            # 其他
            'create_flow_count': '创建会话数',
            'close_flow_count': '关闭会话数',
            'create_flow_countps': '每秒创建会话数',
            'close_flow_countps': '每秒关闭会话数',
            'tcp_disorder_packet': 'TCP乱序包数',
            'client_tcp_disorder_packet': '客户端TCP乱序包数',
            'server_tcp_disorder_packet': '服务器TCP乱序包数',
            'slowconnect': '慢连接次数',
            'slowconnect_ratio': '慢连接占比',
            'client_slowconnect': '客户端慢连接次数',
            'server_slowconnect': '服务器慢连接次数',
            'connection_type': '连接类型',
            'tcp_port_reused_failed': '端口复用失败次数'
        }

    def _translate_field_name(self, field_name: str) -> str:
        """翻译字段名"""
        return self.field_translations.get(field_name, field_name)

    def normalize_field_name(self, field_name: str) -> str:
        """标准化字段名，支持中英文输入"""
        # 如果是中文，转换为英文
        if field_name in self.reverse_field_translations:
            return self.reverse_field_translations[field_name]
        # 如果已经是英文，直接返回
        return field_name

    def _load_knowledge_base(self) -> List[Dict[str, Any]]:
        """加载知识库"""
        return [
            {
                'id': 'NET-AVAIL_CONNECT-FAILED_1',
                'category': '网络可用性/连接失败/未完成三次握手情况',
                'query_type': '查询TCP会话',
                'required_fields': ['server_connection_noresponse_rate', 'server_total_packet'],
                'condition_expr': 'server_connection_noresponse_rate == 100 && server_total_packet == 0'
            },
            {
                'id': 'NET-AVAIL_CONNECT-FAILED_2',
                'category': '网络可用性/连接失败/SYN和SYNACK不匹配',
                'query_type': '在线TCP交易解码',
                'required_fields': [],  # 不需要预定义字段，通过表达式动态查找
                'condition_expr': 'syn_pkt = find_pkt(tcp.flag.syn == 1 && tcp.flag.ack != 1); syn_ack_pkt = find_pkt(tcp.flag.syn == 1 && tcp.flag.ack == 1); syn_ack_pkt.ack != syn_pkt.seq + 1',
                'decode_params': '解码该会话前10个包，时间范围为会话创建时间前2s，limit10个包'
            },
            {
                'id': 'NET-AVAIL_CONNECT-FAILED_3',
                'category': '网络可用性/连接失败/SYN包发送收到RST',
                'query_type': '查询TCP会话',
                'required_fields': ['server_connection_rst', 'server_connection_rst_rate'],
                'condition_expr': 'server_connection_rst > 0 && server_connection_rst_rate > 0'
            },
            {
                'id': 'NET-AVAIL_CONNECT-FAILED_4',
                'category': '网络可用性/连接失败/服务器端口不通',
                'query_type': '查询TCP会话',
                'required_fields': ['tcp_syn_packet', 'server_tcp_rst_packet', 'connection_succ_count'],
                'condition_expr': 'tcp_syn_packet > 0 && server_tcp_rst_packet > 0 && connection_succ_count == 0'
            },
            {
                'id': 'NET-AVAIL_CONNECT-FAILED_5A',
                'category': '网络可用性/连接失败/防火墙策略-静默丢弃',
                'query_type': '查询TCP会话',
                'required_fields': ['tcp_syn_packet', 'tcp_syn_retrans_packet', 'server_total_packet', 'connection_succ_count'],
                'condition_expr': 'tcp_syn_packet > 0 && tcp_syn_retrans_packet > 0 && server_total_packet == 0 && connection_succ_count == 0'
            },
            {
                'id': 'NET-AVAIL_CONNECT-FAILED_5B',
                'category': '网络可用性/连接失败/防火墙策略-拒绝',
                'query_type': '查询TCP会话',
                'required_fields': ['tcp_syn_packet', 'server_tcp_rst_packet', 'server_connection_rst'],
                'condition_expr': 'tcp_syn_packet > 0 && server_tcp_rst_packet > 0 && server_connection_rst > 0'
            },
            {
                'id': 'NET-AVAIL_CONNECT-FAILED_6',
                'category': '网络可用性/连接失败/服务器负载过高',
                'query_type': '查询TCP会话',
                'required_fields': ['tcp_three_handshake', 'server_tcp_window_0', 'tcp_transaction_no_response_count', 'connection_succ_count'],
                'condition_expr': 'tcp_three_handshake > 0 && connection_succ_count > 0 && (server_tcp_window_0 > 0 || tcp_transaction_no_response_count > 0)'
            },
            # 新增TCP解码相关的知识库条目
            {
                'id': 'NET-DECODE_HANDSHAKE_1',
                'category': 'TCP解码/握手异常/无服务器响应',
                'query_type': '在线TCP交易解码',
                'required_fields': ['response_packets', 'server_response_time'],
                'condition_expr': 'response_packets == 0 && server_response_time == 0',
                'decode_params': '解码该会话前10个包，时间范围为会话创建时间前2s，limit10个包'
            },
            {
                'id': 'NET-DECODE_HANDSHAKE_2',
                'category': 'TCP解码/握手异常/重传检测',
                'query_type': '在线TCP交易解码',
                'required_fields': ['has_retransmission', 'syn_packet_count'],
                'condition_expr': 'has_retransmission == 1 || syn_packet_count > 2',
                'decode_params': '解码该会话前10个包，时间范围为会话创建时间前2s，limit10个包'
            },
            {
                'id': 'NET-DECODE_HANDSHAKE_3',
                'category': 'TCP解码/握手异常/RST包检测',
                'query_type': '在线TCP交易解码',
                'required_fields': ['rst_packet_count'],
                'condition_expr': 'rst_packet_count > 0',
                'decode_params': '解码该会话前10个包，时间范围为会话创建时间前2s，limit10个包'
            }
        ]

    def evaluate_expr(self, expr: str, metrics: Dict[str, Any]) -> bool:
        """评估表达式"""
        try:
            # 替换表达式中的字段名为实际值
            eval_expr = expr
            for field_name, metric_data in metrics.items():
                value = metric_data.get('value', 0) if isinstance(metric_data, dict) else metric_data
                eval_expr = eval_expr.replace(field_name, str(value))

            # 替换逻辑运算符
            eval_expr = eval_expr.replace('&&', ' and ')
            eval_expr = eval_expr.replace('||', ' or ')
            eval_expr = eval_expr.replace('!=', ' != ')
            eval_expr = eval_expr.replace('==', ' == ')
            eval_expr = eval_expr.replace('>=', ' >= ')
            eval_expr = eval_expr.replace('<=', ' <= ')
            eval_expr = eval_expr.replace('>', ' > ')
            eval_expr = eval_expr.replace('<', ' < ')

            # 安全评估表达式
            return eval(eval_expr)
        except Exception as e:
            print(f"表达式评估失败: {expr}, 错误: {e}")
            return False

    def analyze_knowledge_base_matches(self, user_query_data: Dict[str, Any],
                                     tcp_decode_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """分析知识库匹配情况"""
        # 标准化用户输入的字段名
        normalized_metrics = {}
        for field_name, value in user_query_data.items():
            normalized_field = self.normalize_field_name(field_name)
            normalized_metrics[normalized_field] = value

        # 如果有TCP解码数据，合并解码指标
        decode_metrics = {}
        if tcp_decode_data and hasattr(self, 'tcp_decode_metrics'):
            decode_metrics = self.tcp_decode_metrics

        full_matches = []
        partial_matches = []
        need_more_info = []
        unmatched = []

        for kb_item in self.knowledge_base:
            kb_id = kb_item['id']
            required_fields = set(kb_item['required_fields'])

            # 根据查询类型选择合适的数据源
            if kb_item['query_type'] == '查询TCP会话':
                available_fields = set(normalized_metrics.keys())
                data_source = normalized_metrics
            else:  # 在线TCP交易解码
                available_fields = set(decode_metrics.keys())
                data_source = decode_metrics

            # 检查字段覆盖情况
            missing_fields = required_fields - available_fields
            available_required_fields = required_fields & available_fields

            if not missing_fields:
                # 所有字段都有，检查条件是否满足
                if kb_item['query_type'] == '查询TCP会话':
                    condition_met = self.evaluate_expr(kb_item['condition_expr'], data_source)
                else:
                    condition_met = self.evaluate_tcp_decode_expr(kb_item['condition_expr'])

                if condition_met:
                    full_matches.append(kb_id)
                else:
                    unmatched.append(kb_id)
            elif available_required_fields:
                # 部分字段匹配
                partial_matches.append(kb_id)
            else:
                # 完全没有匹配的字段
                need_more_info.append(kb_id)

        # 生成补充查询
        additional_query = self._generate_additional_query(partial_matches, need_more_info, normalized_metrics)

        status = "完全匹配" if full_matches and not partial_matches and not need_more_info else "需要二次查询"

        return {
            'status': status,
            'full_matches': full_matches,
            'partial_matches': partial_matches,
            'need_more_info': need_more_info,
            'unmatched': unmatched,
            'additional_query': additional_query,
            'missing_fields_detail': self._get_missing_fields_detail(partial_matches, need_more_info, normalized_metrics),
            'tcp_decode_metrics': decode_metrics if decode_metrics else None
        }

    def _generate_additional_query(self, partial_matches: List[str], need_more_info: List[str],
                                 current_metrics: Dict[str, Any]) -> List[Dict[str, str]]:
        """生成补充查询"""
        tcp_session_fields = set()
        tcp_decode_queries = []

        # 收集所有需要的TCP会话字段
        for kb_id in partial_matches + need_more_info:
            kb_item = next((item for item in self.knowledge_base if item['id'] == kb_id), None)
            if not kb_item:
                continue

            if kb_item['query_type'] == '查询TCP会话':
                for field in kb_item['required_fields']:
                    if field not in current_metrics:
                        tcp_session_fields.add(field)
            elif kb_item['query_type'] == '在线TCP交易解码':
                if 'decode_params' in kb_item:
                    tcp_decode_queries.append(kb_item['decode_params'])

        additional_queries = []

        # 添加TCP会话查询
        if tcp_session_fields:
            # 转换为中文字段名
            chinese_fields = [self._translate_field_name(field) for field in tcp_session_fields]
            query_str = f"查询TCP会话: {', '.join(chinese_fields)}"
            additional_queries.append({'query': query_str})

        # 添加TCP解码查询
        for decode_query in set(tcp_decode_queries):  # 去重
            additional_queries.append({'query': f"在线TCP交易解码: {decode_query}"})

        return additional_queries

    def _get_missing_fields_detail(self, partial_matches: List[str], need_more_info: List[str],
                                 current_metrics: Dict[str, Any]) -> Dict[str, List[str]]:
        """获取缺失字段详情"""
        missing_detail = {}

        for kb_id in partial_matches + need_more_info:
            kb_item = next((item for item in self.knowledge_base if item['id'] == kb_id), None)
            if not kb_item:
                continue

            required_fields = set(kb_item['required_fields'])
            available_fields = set(current_metrics.keys())
            missing_fields = required_fields - available_fields

            if missing_fields:
                # 转换为中文字段名
                chinese_missing = [self._translate_field_name(field) for field in missing_fields]
                missing_detail[kb_id] = chinese_missing

        return missing_detail

    def _extract_tcp_decode_metrics(self) -> Dict[str, Any]:
        """从TCP交易解码数据中提取关键指标用于Expr评估"""
        if not self.packet_sequence:
            return {}

        # 将包序列存储为可查询的结构
        self.packets = self.packet_sequence

        # 基本统计指标
        metrics = {
            'total_packets': len(self.packet_sequence),
            'client_packets': len([p for p in self.packet_sequence if p.get('direction') == 1]),
            'server_packets': len([p for p in self.packet_sequence if p.get('direction') == 2]),
            'request_bytes': self.tcp_transaction_data.get('request_bytes', 0),
            'response_bytes': self.tcp_transaction_data.get('response_bytes', 0),
            'request_packets': self.tcp_transaction_data.get('request_packets', 0),
            'response_packets': self.tcp_transaction_data.get('response_packets', 0),
            'server_response_time': self.tcp_transaction_data.get('server_response_time', 0),
            'has_retransmission': 1 if self.tcp_transaction_data.get('retransmit', 0) > 0 else 0
        }

        return metrics

    def find_pkt(self, condition: str) -> Optional[Dict[str, Any]]:
        """查找满足条件的第一个包"""
        if not hasattr(self, 'packets'):
            return None

        for pkt in self.packets:
            if self._evaluate_packet_condition(pkt, condition):
                return pkt
        return None

    def find_pkts(self, condition: str) -> List[Dict[str, Any]]:
        """查找所有满足条件的包"""
        if not hasattr(self, 'packets'):
            return []

        result = []
        for pkt in self.packets:
            if self._evaluate_packet_condition(pkt, condition):
                result.append(pkt)
        return result

    def _evaluate_packet_condition(self, pkt: Dict[str, Any], condition: str) -> bool:
        """评估单个包是否满足条件"""
        try:
            # 替换TCP标志位条件
            eval_condition = condition
            flags = pkt.get('tcp_flags', [])

            # 处理tcp.flag.xxx条件
            eval_condition = eval_condition.replace('tcp.flag.syn', '1' if 'SYN' in flags else '0')
            eval_condition = eval_condition.replace('tcp.flag.ack', '1' if 'ACK' in flags else '0')
            eval_condition = eval_condition.replace('tcp.flag.rst', '1' if 'RST' in flags else '0')
            eval_condition = eval_condition.replace('tcp.flag.fin', '1' if 'FIN' in flags else '0')
            eval_condition = eval_condition.replace('tcp.flag.psh', '1' if 'PSH' in flags else '0')
            eval_condition = eval_condition.replace('tcp.flag.urg', '1' if 'URG' in flags else '0')

            # 处理其他TCP字段
            eval_condition = eval_condition.replace('tcp.seq', str(pkt.get('sequence_number', 0)))
            eval_condition = eval_condition.replace('tcp.ack', str(pkt.get('ack_number', 0)))
            eval_condition = eval_condition.replace('tcp.len', str(pkt.get('payload_length', 0)))
            eval_condition = eval_condition.replace('tcp.window', str(pkt.get('window_size', 0)))

            # 处理方向
            eval_condition = eval_condition.replace('direction', str(pkt.get('direction', 0)))

            # 替换逻辑运算符
            eval_condition = eval_condition.replace('&&', ' and ')
            eval_condition = eval_condition.replace('||', ' or ')
            eval_condition = eval_condition.replace('!=', ' != ')
            eval_condition = eval_condition.replace('==', ' == ')

            return eval(eval_condition)
        except Exception as e:
            print(f"包条件评估失败: {condition}, 错误: {e}")
            return False

    def evaluate_tcp_decode_expr(self, expr: str) -> bool:
        """评估类似Wireshark的TCP解码表达式"""
        try:
            import re

            # 分割表达式为语句
            statements = [s.strip() for s in expr.split(';') if s.strip()]

            variables = {}
            conditions = []

            for statement in statements:
                # 检查是否是变量赋值
                assign_match = re.match(r'(\w+)\s*=\s*find_pkt\(([^)]+)\)', statement)
                if assign_match:
                    var_name, condition = assign_match.groups()
                    pkt = self.find_pkt(condition)
                    variables[var_name] = pkt
                else:
                    # 这是一个条件表达式
                    conditions.append(statement)

            # 处理条件表达式
            final_conditions = []
            for condition in conditions:
                eval_condition = condition

                # 替换变量引用
                for var_name, pkt in variables.items():
                    if pkt is None:
                        # 如果包不存在，整个表达式为False
                        return False

                    # 替换包字段访问
                    eval_condition = eval_condition.replace(f'{var_name}.seq', str(pkt.get('sequence_number', 0)))
                    eval_condition = eval_condition.replace(f'{var_name}.ack', str(pkt.get('ack_number', 0)))
                    eval_condition = eval_condition.replace(f'{var_name}.len', str(pkt.get('payload_length', 0)))
                    eval_condition = eval_condition.replace(f'{var_name}.window', str(pkt.get('window_size', 0)))

                # 替换基本指标
                if hasattr(self, 'tcp_decode_metrics'):
                    for field_name, value in self.tcp_decode_metrics.items():
                        eval_condition = eval_condition.replace(field_name, str(value))

                # 替换逻辑运算符
                eval_condition = eval_condition.replace('&&', ' and ')
                eval_condition = eval_condition.replace('||', ' or ')

                final_conditions.append(eval_condition)

            # 如果没有条件，说明只是变量赋值，返回True
            if not final_conditions:
                return True

            # 组合所有条件
            final_expr = ' and '.join(f'({cond})' for cond in final_conditions)

            return eval(final_expr)

        except Exception as e:
            print(f"TCP解码表达式评估失败: {expr}, 错误: {e}")
            if 'final_expr' in locals():
                print(f"处理后的表达式: {final_expr}")
            return False


def preprocess_network_data(output_list: List[str]) -> str:
    """
    预处理网络数据的便捷函数

    Args:
        output_list: 包含JSON字符串的列表

    Returns:
        格式化的分析文本
    """
    preprocessor = NetworkDataPreprocessor()
    return preprocessor.preprocess(output_list)


def analyze_tcp_session_with_knowledge_base(user_input: str, tcp_metrics: Dict[str, Any]) -> Dict[str, Any]:
    """
    使用知识库分析TCP会话

    Args:
        user_input: 用户输入的查询内容
        tcp_metrics: TCP指标数据，格式为 {指标名: 值}

    Returns:
        分析结果，包含匹配状态、完全匹配、部分匹配、需要更多信息等
    """
    preprocessor = NetworkDataPreprocessor()

    # 解析用户输入，提取会话信息
    meta_info = _extract_session_info_from_input(user_input)

    # 分析知识库匹配
    analysis_result = preprocessor.analyze_knowledge_base_matches(tcp_metrics)
    analysis_result['meta_info'] = meta_info

    return analysis_result


def _extract_session_info_from_input(user_input: str) -> str:
    """从用户输入中提取会话信息"""
    # 提取关键信息
    patterns = {
        '链路id': r'链路id[：:]\s*(\d+)',
        '客户端': r'客户端[：:]\s*([\d\.]+)',
        '客户端端口': r'客户端端口[：:]\s*(\d+)',
        '服务器': r'服务器[：:]\s*([\d\.]+)',
        '服务器端口': r'服务器端口[：:]\s*(\d+)',
        '会话开始时间': r'会话开始时间[：:]\s*([\d/\s:]+)',
        '会话结束时间': r'会话结束时间[：:]\s*([\d/\s:]+)'
    }

    extracted_info = []
    for key, pattern in patterns.items():
        match = re.search(pattern, user_input)
        if match:
            extracted_info.append(f"{key}: {match.group(1)}")

    return ' '.join(extracted_info)


def main(arg1):
    preprocess_network_data(arg1)