# UI 预览版设计

TCP会话表(调用stats_mcp_server_official.py里面的查询接口)
__________________________________________________________________________________________________________
| 点击指定查询时间范围 | 点击指定链路 | 下拉选择timeunit | 点击选择查询哪些指标 | 指定过滤条件 | 点击指定查询key |
——————————————————————————————————————————————————————————————————————————————————————————————————————————

<!-- 
## 网络层可用性
### 评估指标：连接成功状态 分数评估
￮计算指标
如果 连接失败率大于5%或客户端TCP重置包+服务器TCP重置包>0
那么则显示 网络可用性那列 连接失败
 -->

___________________________________________________________________________________________________________
|AI分析| 客户端 | 客户端端口 | 服务器 | 服务器端口 | 连接失败率 | 客户端TCP重置包 | 服务器TCP重置包| 网络可用性|
| [button] | 10.0.0.15 | 45678 | 203.45.67.89 | 100% | 0 | 0 | 连接失败 [分数] |
———————————————————————————————————————————————————————————————————————————————————————————————————————————


<!-- button点击后为在最下面的AI分析窗口开始执行分析任务 
是一个动态的过程，需要一步一步展示
后台会一步一步的返回当前的执行状态
格式为
{
"action": "正在分析已有指标",
"thought": "考虑之前的步骤和后续步骤",
"result": "分析已有指标中..."
}
result 显示在action下面，md格式显示

 -->

-----------------------AI分析-------------------
正在分析客户端为10.0.0.15, 服务器为203.45.67.89的会话连接失败问题...
* 分析已有指标中...
* 已完成研判场景查询...
* 正在匹配研判场景...
* 缺失指标信息，正在补充查询...
* 补充查询完成，正在生成分析报告...
* 分析报告生成完成，正在提供优化建议...
-----------------------AI分析-------------------

