# 网络事件分析梳理

## 网络层可用性
### 评估指标：连接成功状态
￮计算指标
▪TCP建连成功率=三次握手成功次数/总尝试次数
▪TCP RST次数：对应“异常关闭”

￮研判场景
▪1.未完成完整三次握手情况
•用户/客户端的体验：用户点击后，应用会显示“正在连接…”或长时间转圈，经过一段明显的等待时间（数秒到数十秒）后，最终提示“连接超时”(Connection timed out)。 
•网络分析工具看到的现象：客户端在不断地尝试，但得不到回应。在这个过程中，分析工具没有看到任何从服务器返回的数据包（特别是SYN/ACK包）。 
￮客户端 → 服务器: [SYN] 
￮(等待…) 
￮客户端 → 服务器: [SYN] (第一次重传) 
￮(等待更长时间…) 
￮客户端 → 服务器: [SYN] (第二次重传) 
￮...最终客户端放弃。 
•工具的诊断结论： 
￮诊断名称： 连接超时 (Connection Timeout) / SYN丢包。 
￮归类： 网络可用性 - 连接失败。 
￮根因分析指向： 
▪网络路径问题： SYN包在去往服务器的路上被丢弃（如网络拥塞、路由黑洞）。 
▪防火墙静默丢弃： 防火墙配置了DROP规则，它“假装没看见”SYN包，不作任何回应。 
▪服务器无响应： SYN包到达了服务器，但服务器的SYN/ACK响应在返回途中丢失了。 
▪服务器SYN队列溢出： 服务器因负载过高或遭受SYN Flood攻击，其用于处理新连接的队列已满，直接丢弃了新的SYN包。

▪2.SYN和SYNACK不匹配
•用户/客户端的体验：用户体验通常也是“连接超时”。因为客户端的TCP协议栈在收到一个不匹配的SYN/ACK后，会认为它是一个无效的、错误的响应，会将其丢弃，然后继续等待正确的SYN/ACK，直到超时。 
•网络分析工具看到的现象：工具的协议解析器会立刻标记这个SYN/ACK包为异常或畸形，因为它违反了TCP协议的规定（确认号必须是收到的序列号+1）。 
￮客户端 → 服务器: [SYN]，其中序列号 Seq=X。
￮服务器 → 客户端: [SYN, ACK]，其中确认号 Ack 不等于 X+1。 
•工具的诊断结论： 
￮诊断名称： TCP协议异常 / 握手确认错误。 
￮归类： 网络可用性 - 连接失败。 
￮根因分析指向：
▪有状态网络设备故障： 路径上的负载均衡器、NAT网关或防火墙的状态表发生错乱，将不相关的会话信息串在了一起。 
▪非对称路由： 请求和响应走了不同的路径，导致路径上的状态设备无法正确匹配会话。 
▪中间人攻击 (MitM)： 有攻击者在中间伪造SYN/ACK包，企图劫持或干扰连接。 
▪服务器TCP协议栈Bug： 极其罕见，但理论上可能存在。

▪3.SYN包发送，收到RST
•用户/客户端的体验：用户点击链接或按钮后，几乎是瞬间（毫秒级）就看到“连接被拒绝”(Connection refused)或类似的错误提示。没有漫长的等待，只有一个迅速而决绝的“不”。 
•网络分析工具看到的现象：工具会捕获到一个非常简短、清晰的交互。这是一个完整的、但失败的“握手尝试”。服务器明确地拒绝了连接请求。 
￮客户端 → 服务器: [SYN] (客户端：“你好，我想和你建立连接。”) 
￮服务器 → 客户端: [RST, ACK] (服务器：“滚开！”) 
•工具的诊断结论：
￮诊断名称： 连接被拒绝 (Connection Refused)。 
￮归类： 网络可用性 - 连接失败。 
￮根因分析指向： 
▪服务器端口未监听： 这是最常见的原因。服务器上根本没有应用程序在等待这个端口的连接。就像你敲了一扇没人在家的门。 
▪防火墙策略： 中间或服务器上的防火墙配置了REJECT规则，它主动地拒绝了该连接请求，并礼貌性地（或者说不留情面地）回复了一个RST包。

▪4.服务器端口不通
•用户/客户端的体验：“连接失败”、“网站打不开”。
•网络分析工具看到的现象： 
￮客户端发出一个 TCP SYN 包，请求与服务器的某个端口建立连接。 
￮服务器的操作系统收到这个包，检查后发现没有任何应用程序在监听这个端口。 
￮于是，服务器的操作系统内核会立即回复一个 TCP RST (Reset) 包。
•工具的诊断结论： 从网络流量上看，这是一个非常明确的“连接被拒绝”事件。整个交互完全发生在网络TCP层，客户端甚至没有机会与服务器上的应用程序对话。因此，对于网络分析工具来说，这是一个典型的网络连接层面的可用性问题。它表现为“连接建立失败”。
▪防火墙策略
•用户/客户端的体验：“连接超时”、“网站打不开”。 
•网络分析工具看到的现象：
￮情况A（静默丢弃 - Drop）： 客户端发出 TCP SYN 包。 位于服务器前或服务器上的防火墙，根据策略直接将这个包丢弃，不作任何回应。 客户端等不到 SYN/ACK 回应，会在超时后重传 SYN 包。 多次重传后，客户端最终放弃，连接超时。 
￮情况B（拒绝 - Reject）： 客户端发出 TCP SYN 包。 防火墙根据策略，主动回复一个 TCP RST 包或 ICMP Port Unreachable 消息。 
•工具的诊断结论： 无论是哪种情况，问题都表现为网络通信的中断。在情况A中，工具看到的是有去无回的单向流量和大量的重传；在情况B中，看到的是连接被直接拒绝。这些都是发生在网络层和传输层的典型问题。

▪5.服务器负载过高
•用户/客户端的体验：“连接超时”或“连接上了但网页一直转圈，没任何内容”。
•网络分析工具看到的现象：
￮客户端发出 TCP SYN 包。 
￮服务器的操作系统内核其实是空闲的，它回复了 SYN/ACK，完成了三次握手。从TCP层面看，连接已经建立了！
￮但是，由于服务器上的应用程序（比如Web Server）因为CPU满载、线程池耗尽等原因，根本没有能力去 accept() 这个新的连接，或者 accept() 了之后没有能力处理请求。
￮结果就是，客户端虽然建立了TCP连接，但迟迟等不到任何应用数据（比如HTTP响应头）。最终，客户端或服务器可能会因为超时而关闭连接。
•工具的诊断结论： 工具观察到“TCP连接成功，但应用层长时间无数据交互”，或者观察到“服务器TCP接收窗口（Receive Window）持续为0”，这暗示服务器的应用层已经无法接收更多数据了。虽然根因在服务器内部，但它最先暴露出的症状是网络通信的停滞。对于一个希望提供端到端（End-to-End）视图的运维产品来说，它必须能捕捉到这种“伪连接成功”的场景，并将其归类为服务不可用。