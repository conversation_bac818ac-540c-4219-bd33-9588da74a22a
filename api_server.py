#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络分析API服务器
实现 /v1/query/metric 接口，用于网络可用性分析和指标查询
"""

import json
import traceback
from datetime import datetime
from typing import Dict, List, Any, Optional
from flask import Flask, request, jsonify
from flask_cors import CORS

# 导入现有的统计API客户端
from stats_mcp_server_official import StatsApiClient

app = Flask(__name__)
CORS(app)  # 启用跨域支持

# 全局API客户端实例
api_client = None

class NetworkAnalysisEngine:
    """网络分析引擎，用于处理网络可用性分析"""
    
    def __init__(self, stats_client: StatsApiClient):
        self.stats_client = stats_client
    
    def analyze_tcp_connection_failure(self, query_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析TCP连接失败情况
        
        Args:
            query_data: 查询数据，包含服务器IP、端口、客户端IP等信息
            
        Returns:
            分析结果
        """
        try:
            # 提取查询参数
            server_ip = query_data.get("server_ip_addr")
            server_port = query_data.get("server_port")
            client_ip = query_data.get("client_ip_addr")
            client_port = query_data.get("client_port")
            start_time = query_data.get("flow_start_time")
            end_time = query_data.get("flow_end_time")
            failure_rate = query_data.get("tcp_connect_failure_rate", 0)
            
            # 构建查询条件
            filter_conditions = []
            if server_ip:
                filter_conditions.append(f"server_ip_addr={server_ip}")
            if server_port:
                filter_conditions.append(f"server_port={server_port}")
            if client_ip:
                filter_conditions.append(f"client_ip_addr={client_ip}")
            if client_port:
                filter_conditions.append(f"client_port={client_port}")
            
            filter_condition = " && ".join(filter_conditions) if filter_conditions else ""
            
            # 查询TCP流统计数据
            tcp_flow_result = self.stats_client.query_stats_data(
                table="tcp_flow",
                begintime=start_time,
                endtime=end_time,
                fields=["server_ip_addr", "server_port", "client_ip_addr", "client_port", 
                       "total_byte", "total_packet", "flow_duration", "tcp_status",
                       "client_total_byte", "server_total_byte"],
                keys=["server_ip_addr", "server_port", "client_ip_addr", "client_port"],
                filter_condition=filter_condition,
                topcount=1000
            )
            
            # 分析结果
            analysis_result = {
                "connection_analysis": {
                    "failure_rate": failure_rate,
                    "severity": "高" if failure_rate >= 80 else "中" if failure_rate >= 50 else "低",
                    "tcp_flow_data": tcp_flow_result
                },
                "recommendations": []
            }
            
            # 根据失败率提供建议
            if failure_rate >= 80:
                analysis_result["recommendations"].extend([
                    "检查服务器是否正常运行",
                    "验证网络连通性",
                    "检查防火墙配置"
                ])
            elif failure_rate >= 50:
                analysis_result["recommendations"].extend([
                    "监控服务器负载",
                    "检查网络延迟"
                ])
            
            return analysis_result
            
        except Exception as e:
            return {
                "error": f"TCP连接失败分析出错: {str(e)}",
                "traceback": traceback.format_exc()
            }
    
    def analyze_network_availability(self, analysis_type: str, query_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        网络可用性分析主入口
        
        Args:
            analysis_type: 分析类型
            query_data: 查询数据
            
        Returns:
            分析结果
        """
        if analysis_type == "网络可用性-连接失败":
            return self.analyze_tcp_connection_failure(query_data)
        else:
            return {
                "error": f"不支持的分析类型: {analysis_type}",
                "supported_types": ["网络可用性-连接失败"]
            }

def setup_api_client(url: str = "https://192.168.163.209:8080/") -> bool:
    """设置API客户端连接"""
    global api_client
    try:
        import ssl
        ssl._create_default_https_context = ssl._create_unverified_context
        
        api_client = StatsApiClient(url, "admin", "D&^4Vs!(", "6.1")
        return api_client.login()
    except Exception as e:
        print(f"API连接设置失败: {str(e)}")
        return False

@app.route('/v1/query/metric', methods=['POST'])
def query_metric():
    """
    网络指标查询接口
    
    接收JSON格式的查询请求，返回分析结果
    """
    try:
        # 检查API客户端连接
        if api_client is None or not api_client.session:
            return jsonify({
                "status": "连接失败",
                "error": "API客户端未连接，请先设置连接",
                "full_matches": [],
                "partial_matches": [],
                "need_more_info": [],
                "additional_query": [],
                "llm_output": []
            }), 500
        
        # 解析请求数据
        request_data = request.get_json()
        if not request_data:
            return jsonify({
                "status": "参数错误",
                "error": "请求体为空或格式错误",
                "full_matches": [],
                "partial_matches": [],
                "need_more_info": [],
                "additional_query": [],
                "llm_output": []
            }), 400
        
        # 提取请求参数
        analysis_type = request_data.get("analysis", "")
        use_llm = request_data.get("use_llm", True)
        run_mode = request_data.get("run_mode", "auto")
        query_list = request_data.get("query", [])
        
        if not query_list:
            return jsonify({
                "status": "参数错误", 
                "error": "缺少query参数",
                "full_matches": [],
                "partial_matches": [],
                "need_more_info": [],
                "additional_query": [],
                "llm_output": []
            }), 400
        
        # 创建网络分析引擎
        analysis_engine = NetworkAnalysisEngine(api_client)
        
        # 处理查询列表
        results = []
        for query_item in query_list:
            table = query_item.get("table", "")
            query_start_time = query_item.get("query_start_time", "")
            query_end_time = query_item.get("query_end_time", "")
            query_type = query_item.get("type", "metric")
            data = query_item.get("data", {})
            
            # 执行分析
            if query_type == "metric" and analysis_type:
                analysis_result = analysis_engine.analyze_network_availability(analysis_type, data)
                results.append({
                    "table": table,
                    "time_range": f"{query_start_time} - {query_end_time}",
                    "type": query_type,
                    "analysis_result": analysis_result
                })
            else:
                # 直接查询统计数据
                if table and query_start_time and query_end_time:
                    # 构建基本查询参数
                    fields = ["server_ip_addr", "server_port", "client_ip_addr", "total_byte", "total_packet"]
                    keys = ["server_ip_addr", "server_port"]
                    
                    query_result = api_client.query_stats_data(
                        table=table,
                        begintime=query_start_time,
                        endtime=query_end_time,
                        fields=fields,
                        keys=keys,
                        topcount=100
                    )
                    
                    results.append({
                        "table": table,
                        "time_range": f"{query_start_time} - {query_end_time}",
                        "type": query_type,
                        "query_result": query_result
                    })
        
        # 根据结果判断状态
        if results:
            # 检查是否需要更多信息或二次查询
            need_more_analysis = any(
                "error" in result.get("analysis_result", {}) or 
                not result.get("analysis_result", {}).get("connection_analysis", {}).get("tcp_flow_data", {}).get("success", False)
                for result in results
            )
            
            if need_more_analysis:
                status = "需要二次查询"
                additional_queries = [
                    {
                        "table": "tcp_server_port",
                        "fields": "server_ip_addr,server_port,visit_count,total_byte",
                        "keys": "server_ip_addr,server_port",
                        "reason": "获取服务端口访问统计"
                    }
                ]
            else:
                status = "查询成功"
                additional_queries = []
            
            return jsonify({
                "status": status,
                "full_matches": results,
                "partial_matches": [],
                "need_more_info": [],
                "additional_query": additional_queries,
                "llm_output": [f"分析类型: {analysis_type}, 运行模式: {run_mode}"]
            })
        else:
            return jsonify({
                "status": "无匹配结果",
                "full_matches": [],
                "partial_matches": [],
                "need_more_info": ["请检查查询参数和时间范围"],
                "additional_query": [],
                "llm_output": []
            })
            
    except Exception as e:
        return jsonify({
            "status": "服务器错误",
            "error": str(e),
            "traceback": traceback.format_exc(),
            "full_matches": [],
            "partial_matches": [],
            "need_more_info": [],
            "additional_query": [],
            "llm_output": []
        }), 500

@app.route('/v1/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "api_connected": api_client is not None and bool(api_client.session)
    })

@app.route('/v1/setup', methods=['POST'])
def setup_connection():
    """设置API连接接口"""
    try:
        request_data = request.get_json()
        url = request_data.get("url", "https://192.168.163.209:8080/")
        
        success = setup_api_client(url)
        
        return jsonify({
            "success": success,
            "message": "API连接设置成功" if success else "API连接设置失败",
            "url": url
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"设置连接时出错: {str(e)}"
        }), 500

if __name__ == '__main__':
    # 启动时尝试设置默认连接
    print("正在启动网络分析API服务器...")
    setup_success = setup_api_client()
    if setup_success:
        print("✓ API连接设置成功")
    else:
        print("⚠ API连接设置失败，请稍后通过 /v1/setup 接口设置")
    
    print("API服务器启动在 http://localhost:8888")
    print("主要接口:")
    print("  POST /v1/query/metric - 网络指标查询")
    print("  GET  /v1/health      - 健康检查")
    print("  POST /v1/setup       - 设置API连接")

    app.run(host='0.0.0.0', port=8888, debug=True)
