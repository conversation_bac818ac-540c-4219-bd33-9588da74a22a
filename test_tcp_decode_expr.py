#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试TCP交易解码的Expr功能
"""

import json
from data_preprocessor import NetworkDataPreprocessor

def test_tcp_decode_expr():
    """测试TCP解码表达式评估"""
    print("=== 测试TCP解码表达式评估 ===")
    
    # 模拟TCP交易解码的原始数据
    tcp_decode_raw_data = {
        "success": True,
        "error_code": 0,
        "message": "TCP交易解析成功",
        "data": {
            "transactions": [
                {
                    "transaction_id": 1,
                    "client_ip": "*************",
                    "client_port": 40276,
                    "server_ip": "*************",
                    "server_port": 80,
                    "request_bytes": 80,
                    "response_bytes": 0,
                    "request_packets": 1,
                    "response_packets": 0,
                    "total_bytes": 80,
                    "total_packets": 1,
                    "business_type": 1,
                    "retransmit": 0,
                    "server_response_time": 0,
                    "client_trans_time": 0,
                    "server_trans_time": 0,
                    "process_time": 0,
                    "bit_rate": 0,
                    "pkt_seq_diagram": [
                        {
                            "ack": 0,
                            "businessType": 1,
                            "direction": 1,
                            "ipid": 14927,
                            "matchPkt": 0,
                            "nextSeq": 1157012144,
                            "payloadlen": 0,
                            "pktNum": 0,
                            "seq": 1157012143,
                            "summaryDecoder": "<PacketSummary>[SYN]序列号=1157012143,确认号=0,窗口=14600,头部长度=40 字节</PacketSummary>",
                            "tcpStatusFlag": -1,
                            "time": 1753163581166521888
                        },
                        {
                            "ack": 1157012144,  # 正确的ACK应该是1157012144 (SYN seq + 1)
                            "businessType": 0,
                            "direction": 2,
                            "ipid": 0,
                            "matchPkt": 0,
                            "nextSeq": 1493615489,
                            "payloadlen": 0,
                            "pktNum": 1,
                            "seq": 1493615488,
                            "summaryDecoder": "<PacketSummary>[ACK, SYN]序列号=1493615488,确认号=1157012144,窗口=28400,头部长度=32 字节</PacketSummary>",
                            "tcpStatusFlag": -1,
                            "time": 1753163581166524473
                        }
                    ]
                }
            ]
        }
    }
    
    # 创建预处理器并解析数据
    preprocessor = NetworkDataPreprocessor()
    preprocessor._parse_tcp_transaction_data(tcp_decode_raw_data)
    
    print("提取的TCP解码指标:")
    for key, value in preprocessor.tcp_decode_metrics.items():
        print(f"  {key}: {value}")
    
    # 测试SYN/ACK匹配表达式
    expr1 = "syn_ack_mismatch == 1"
    result1 = preprocessor.evaluate_tcp_decode_expr(expr1)
    print(f"\n表达式: {expr1}")
    print(f"结果: {result1}")
    
    # 测试包数量表达式
    expr2 = "total_packets >= 2 && syn_packet_count > 0"
    result2 = preprocessor.evaluate_tcp_decode_expr(expr2)
    print(f"\n表达式: {expr2}")
    print(f"结果: {result2}")
    
    # 测试响应相关表达式
    expr3 = "response_packets == 0 && server_response_time == 0"
    result3 = preprocessor.evaluate_tcp_decode_expr(expr3)
    print(f"\n表达式: {expr3}")
    print(f"结果: {result3}")

def test_syn_ack_mismatch_scenario():
    """测试SYN/ACK不匹配的场景"""
    print("\n\n=== 测试SYN/ACK不匹配场景 ===")
    
    # 模拟SYN/ACK不匹配的数据
    tcp_decode_mismatch_data = {
        "success": True,
        "message": "TCP交易解析成功",
        "data": {
            "transactions": [
                {
                    "transaction_id": 1,
                    "retransmit": 0,
                    "server_response_time": 0,
                    "request_bytes": 0,
                    "response_bytes": 0,
                    "request_packets": 1,
                    "response_packets": 1,
                    "pkt_seq_diagram": [
                        {
                            "ack": 0,
                            "direction": 1,
                            "seq": 1000,
                            "summaryDecoder": "<PacketSummary>[SYN]序列号=1000,确认号=0,窗口=14600,头部长度=40 字节</PacketSummary>",
                            "time": 1753163581166521888
                        },
                        {
                            "ack": 999,  # 错误的ACK，应该是1001
                            "direction": 2,
                            "seq": 2000,
                            "summaryDecoder": "<PacketSummary>[ACK, SYN]序列号=2000,确认号=999,窗口=28400,头部长度=32 字节</PacketSummary>",
                            "time": 1753163581166524473
                        }
                    ]
                }
            ]
        }
    }
    
    preprocessor = NetworkDataPreprocessor()
    preprocessor._parse_tcp_transaction_data(tcp_decode_mismatch_data)
    
    print("SYN/ACK不匹配场景的指标:")
    for key, value in preprocessor.tcp_decode_metrics.items():
        print(f"  {key}: {value}")
    
    # 测试知识库匹配
    user_input = "测试SYN/ACK不匹配"
    tcp_metrics = {}  # 没有TCP会话指标
    
    result = preprocessor.analyze_knowledge_base_matches(tcp_metrics, tcp_decode_mismatch_data)
    
    print(f"\n知识库匹配结果:")
    print(f"完全匹配: {result['full_matches']}")
    print(f"部分匹配: {result['partial_matches']}")
    print(f"需要更多信息: {result['need_more_info']}")

def test_normal_handshake_scenario():
    """测试正常握手场景"""
    print("\n\n=== 测试正常握手场景 ===")
    
    # 模拟正常握手的数据
    tcp_decode_normal_data = {
        "success": True,
        "message": "TCP交易解析成功",
        "data": {
            "transactions": [
                {
                    "transaction_id": 1,
                    "retransmit": 0,
                    "server_response_time": 100,
                    "request_bytes": 80,
                    "response_bytes": 200,
                    "request_packets": 1,
                    "response_packets": 1,
                    "pkt_seq_diagram": [
                        {
                            "ack": 0,
                            "direction": 1,
                            "seq": 1000,
                            "summaryDecoder": "<PacketSummary>[SYN]序列号=1000,确认号=0,窗口=14600,头部长度=40 字节</PacketSummary>",
                            "time": 1753163581166521888
                        },
                        {
                            "ack": 1001,  # 正确的ACK
                            "direction": 2,
                            "seq": 2000,
                            "summaryDecoder": "<PacketSummary>[ACK, SYN]序列号=2000,确认号=1001,窗口=28400,头部长度=32 字节</PacketSummary>",
                            "time": 1753163581166524473
                        }
                    ]
                }
            ]
        }
    }
    
    preprocessor = NetworkDataPreprocessor()
    preprocessor._parse_tcp_transaction_data(tcp_decode_normal_data)
    
    print("正常握手场景的指标:")
    for key, value in preprocessor.tcp_decode_metrics.items():
        print(f"  {key}: {value}")
    
    # 测试SYN/ACK匹配表达式
    expr = "syn_ack_mismatch == 1"
    result = preprocessor.evaluate_tcp_decode_expr(expr)
    print(f"\n表达式: {expr}")
    print(f"结果: {result} (应该是False，因为握手正常)")

if __name__ == "__main__":
    test_tcp_decode_expr()
    test_syn_ack_mismatch_scenario()
    test_normal_handshake_scenario()
