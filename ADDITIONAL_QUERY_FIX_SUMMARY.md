# LLM漏掉additional_query中"在线TCP交易解码"问题修复总结

## 问题描述

在网络事件分析系统中，LLM有时会漏掉`additional_query`中的"在线TCP交易解码"部分，导致分析不完整。

### 具体表现

用户输入：
```
客户端: 10.127.104.10 客户端端口: 40276 服务器: 60.28.208.186 服务器端口: 80
会话开始时间: 2025/07/22 13:53:01
会话结束时间: 2025/07/22 13:53:02
查询TCP会话：连接失败率 = 100%
```

**期望输出**：
```json
{
  "additional_query": [
    {
      "query": "查询TCP会话: 连接建立服务器无响应率, 服务器数据包数, TCP同步包, 服务器TCP重置包, 连接建立成功次数"
    },
    {
      "query": "在线TCP交易解码: 解码该会话前10个包，时间范围为会话创建时间前2s，limit10个包"
    }
  ]
}
```

**实际问题**：LLM有时只输出第一个查询，漏掉了"在线TCP交易解码"。

## 根本原因分析

1. **提示词不够明确**：原始提示词对于"在线TCP交易解码"的生成规则不够清晰
2. **缺少强制性规则**：没有明确要求检查所有查询类型
3. **示例不足**：缺少容易出错场景的详细示例
4. **LLM注意力分散**：处理多种查询类型时容易遗漏某些类型

## 解决方案

### 1. 强化提示词规则

在`llm_prompt_template.md`中添加了以下关键规则：

#### 新增规则8：查询类型强制检查原则
```
必须遍历所有partial_matches和need_more_info中的知识库场景，识别所有不同的查询类型
（如"查询TCP会话"、"在线TCP交易解码"），确保每种查询类型都在additional_query中有对应的查询项
```

#### 新增规则9：在线TCP交易解码必须包含原则
```
如果任何知识库场景需要"在线TCP交易解码"，必须在additional_query中包含完整的解码查询，
格式为"在线TCP交易解码: 解码该会话前X个包，时间范围为会话创建时间前Xs，limitX个包"
```

### 2. 增加检查清单

在生成`additional_query`前，要求LLM确认：
- 是否有场景需要"查询TCP会话"？如有，合并所有相关字段
- 是否有场景需要"在线TCP交易解码"？如有，必须包含完整的解码查询
- 每种查询类型都有对应的查询项吗？

### 3. 添加详细示例

#### 示例3：容易漏掉在线TCP交易解码的场景
提供了完整的分析过程和正确输出，明确展示：
- 如何识别需要"在线TCP交易解码"的场景
- 正确的`additional_query`应该包含两种查询类型
- 错误示例和为什么是错误的

### 4. 强化注意事项

添加了重要提醒：
- 必须检查所有partial_matches和need_more_info中的知识库场景
- 如果任何场景需要"在线TCP交易解码"，都必须在additional_query中包含该查询类型

## 修改文件

主要修改了 `llm_prompt_template.md` 文件：

### 关键改进点
1. ✅ 查询类型完整性规则
2. ✅ 在线TCP交易解码强制包含规则  
3. ✅ 查询类型强制检查原则
4. ✅ 在线TCP交易解码必须包含原则
5. ✅ 检查清单
6. ✅ 容易漏掉的场景示例
7. ✅ 错误示例说明

## 验证方法

创建了两个验证脚本：

1. **validate_prompt_improvements.py**：验证提示词改进点是否完整
2. **test_additional_query_fix.py**：实际测试LLM是否能正确生成包含"在线TCP交易解码"的additional_query

## 预期效果

通过这些改进，LLM应该能够：

1. **更准确地识别**所有需要的查询类型
2. **强制包含**"在线TCP交易解码"查询（如果任何场景需要）
3. **减少遗漏**查询类型的情况
4. **提高分析完整性**

## 使用建议

1. **测试验证**：运行 `test_additional_query_fix.py` 进行实际LLM测试
2. **监控效果**：在实际使用中观察LLM是否还会漏掉"在线TCP交易解码"
3. **持续优化**：如果仍有问题，可以进一步强化提示词或调整LLM参数

## 技术细节

### 关键知识库场景
- **NET-AVAIL_CONNECT-FAILED_1**: 需要"查询TCP会话"
- **NET-AVAIL_CONNECT-FAILED_2**: 需要"在线TCP交易解码" ⚠️ 容易被漏掉
- **NET-AVAIL_CONNECT-FAILED_4**: 需要"查询TCP会话"

### 查询类型映射
- **查询TCP会话** → `query_statistics_table` MCP工具
- **在线TCP交易解码** → `get_tcp_transaction_parsing` MCP工具

通过这次修复，系统应该能够更可靠地生成完整的`additional_query`，确保网络事件分析的完整性和准确性。
