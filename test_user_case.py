#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用户提供的具体案例
"""

import json
from data_preprocessor import analyze_tcp_session_with_knowledge_base

def test_user_specific_case():
    """测试用户提供的具体案例"""
    print("=== 测试用户具体案例 ===")
    
    user_input = """
    链路id:2 服务器：192.168.163.209 
    客户端: ************* 客户端端口: 40276 服务器: ************* 服务器端口: 80 
    会话开始时间: 2025/07/22 13:53:01 
    会话结束时间: 2025/07/22 13:53:02 
    查询TCP会话: tcp_connect_failure_rate = 100%
    """
    
    # 用户提供的指标数据
    tcp_metrics = {
        'tcp_connect_failure_rate': 100  # 连接失败率 = 100%
    }
    
    result = analyze_tcp_session_with_knowledge_base(user_input, tcp_metrics)
    
    print("用户输入:")
    print(user_input.strip())
    print("\n提供的指标:")
    print("tcp_connect_failure_rate = 100%")
    
    print("\n=== 分析结果 ===")
    print(f"状态: {result['status']}")
    print(f"完全匹配: {result['full_matches']}")
    print(f"部分匹配: {result['partial_matches']}")
    print(f"需要更多信息: {result['need_more_info']}")
    print(f"不匹配: {result['unmatched']}")
    
    print("\n=== 补充查询建议 ===")
    for i, query in enumerate(result['additional_query'], 1):
        print(f"{i}. {query['query']}")
    
    print("\n=== 缺失字段详情 ===")
    for kb_id, missing_fields in result['missing_fields_detail'].items():
        print(f"{kb_id}: {', '.join(missing_fields)}")
    
    print("\n=== 完整JSON结果 ===")
    print(json.dumps(result, ensure_ascii=False, indent=2))

def test_with_additional_metrics():
    """测试补充指标后的情况"""
    print("\n\n=== 测试补充指标后的情况 ===")
    
    user_input = """
    链路id:2 服务器：192.168.163.209 
    客户端: ************* 客户端端口: 40276 服务器: ************* 服务器端口: 80 
    会话开始时间: 2025/07/22 13:53:01 
    会话结束时间: 2025/07/22 13:53:02 
    查询TCP会话: tcp_connect_failure_rate = 100%
    """
    
    # 假设用户补充了一些指标
    tcp_metrics = {
        'tcp_connect_failure_rate': 100,
        'tcp_syn_packet': 5,
        'tcp_syn_retrans_packet': 3,
        'server_total_packet': 0,
        'connection_succ_count': 0,
        'server_connection_noresponse_rate': 100
    }
    
    result = analyze_tcp_session_with_knowledge_base(user_input, tcp_metrics)
    
    print("补充指标后的分析结果:")
    print(f"状态: {result['status']}")
    print(f"完全匹配: {result['full_matches']}")
    print(f"部分匹配: {result['partial_matches']}")
    print(f"需要更多信息: {result['need_more_info']}")
    print(f"不匹配: {result['unmatched']}")
    
    print("\n剩余补充查询:")
    for i, query in enumerate(result['additional_query'], 1):
        print(f"{i}. {query['query']}")

if __name__ == "__main__":
    test_user_specific_case()
    test_with_additional_metrics()
