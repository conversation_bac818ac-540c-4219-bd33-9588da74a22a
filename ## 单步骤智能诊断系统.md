## 单步骤智能诊断系统 - 整体流程图

> 主要是讨论单步骤的智能诊断

```mermaid
flowchart TD
    A[用户查询输入] --> B[UPM计算评估应用可用性分数]
    B --> C{满足分数要求?}
    C -->|否| D[拒绝服务]
    C -->|是| E[AI初步诊断 - 一级研判]
    
    E --> F[数据预处理器<br/>NetworkDataPreprocessor]
    F --> G[知识库匹配分析<br/>analyze_knowledge_base_matches]
    G --> H{匹配结果}
    
    H -->|100%完全匹配| I[直接输出诊断结论]
    H -->|部分匹配/需要更多信息| J[生成补充查询语句]
    
    J --> K[Agent构建二次查询]
    K --> L[执行补充查询]
    L --> M[TCP会话指标查询]
    L --> N[在线TCP交易解码]
    
    M --> O[数据预处理与格式化]
    N --> O
    O --> P[二级研判 - 知识库再次匹配]
    P --> Q{二次匹配结果}
    
    Q -->|100%匹配| R[结合案例和数据输出最终结论]
    Q -->|仍需补充| S[继续迭代查询]
    
    R --> T[输出诊断报告]
    S --> K
    I --> T
    
    style A fill:#e1f5fe
    style T fill:#c8e6c9
    style F fill:#fff3e0
    style G fill:#fff3e0
    style P fill:#fff3e0
```

## 系统架构图 - 核心组件与数据流
```mermaid
graph TB
    subgraph "用户接口层"
        UI[Web界面/API接口]
        UPM[UPM评估模块]
    end
    
    subgraph "智能诊断引擎"
        subgraph "MCP ReAct Agent"
            AGENT[mcpReActAgentStrategy]
            EXP[ExperienceManager<br/>经验管理器]
            LLM[LLM模型调用]
        end
        
        subgraph "数据处理层"
            PREP[NetworkDataPreprocessor<br/>数据预处理器]
            PARSER[输出解析器<br/>CotAgentOutputParser]
        end
    end
    
    subgraph "知识库系统"
        KB[知识库<br/>Knowledge Base]
        CASES[案例库<br/>Case Library]
        RULES[规则引擎<br/>Rule Engine]
    end
    
    subgraph "MCP服务层"
        MCP_SERVER[stats_mcp_server_official<br/>统计数据查询服务]
        API_CLIENT[StatsApiClient<br/>API客户端]
    end
    
    subgraph "数据源"
        STATS_DB[(统计数据库)]
        TCP_DECODE[TCP解码服务]
        NET_DATA[网络设备数据]
    end
    
    subgraph "输出系统"
        REPORT[诊断报告生成]
        LOG[日志记录]
        METRICS[性能指标]
    end
    
    %% 数据流连接
    UI --> UPM
    UPM --> AGENT
    AGENT --> EXP
    AGENT --> LLM
    AGENT --> PREP
    LLM --> PARSER
    
    AGENT <--> MCP_SERVER
    MCP_SERVER --> API_CLIENT
    API_CLIENT --> STATS_DB
    API_CLIENT --> TCP_DECODE
    TCP_DECODE --> NET_DATA
    
    PREP --> KB
    KB --> CASES
    KB --> RULES
    
    AGENT --> REPORT
    AGENT --> LOG
    AGENT --> METRICS
    
    %% 样式
    classDef userLayer fill:#e3f2fd
    classDef engineLayer fill:#f3e5f5
    classDef knowledgeLayer fill:#e8f5e8
    classDef mcpLayer fill:#fff3e0
    classDef dataLayer fill:#fce4ec
    classDef outputLayer fill:#e0f2f1
    
    class UI,UPM userLayer
    class AGENT,EXP,LLM,PREP,PARSER engineLayer
    class KB,CASES,RULES knowledgeLayer
    class MCP_SERVER,API_CLIENT mcpLayer
    class STATS_DB,TCP_DECODE,NET_DATA dataLayer
    class REPORT,LOG,METRICS outputLayer
```

1. 用户接入层 - 需要兼容多个平台不同的输入
2. 知识库系统
   1. 真实案例知识库
   2. 网络指标案例知识库
   3. Agent经验知识库（包括**正反馈**，**负反馈**经验）
   4. 评分系统
   5. **知识库加密**
3. **ReAct** Agent层
   1. 支持并发调用
   2. 支持接入多平台的服务调用，包括**UPM API**，**RAS API**，**ICI**等
   3. 评分系统（评估准确度，和调用成功率）
   4. **研判模块**（对指标，在线解码等采用硬编码尽可能判断保证100%准确性）
   5. **Experience 管理**（包括正反馈，负反馈经验）
   6. **反推理模块** （防止被反推理原理）
4. MCP服务层
   1. 使用**MCP-ZERO**，**MCP-RAG**重构MCP层
   2. 支持并发调用
5. 输出系统
   1. 全栈监测，**DEEPEVAL**
   2. 性能测试
   3. 准确性测试
   4. 消融实验
6. 业务层
   1. 提供对外API服务，包括health， **FASTAPI**
   2. 提供 restart服务
   3. 支持docker
7. 加密层
   1. **提供整体加密**



## MCP ReAct Agent 详细执行流程


```mermaid
sequenceDiagram
    participant User as 用户
    participant Agent as mcpReActAgent
    participant Exp as ExperienceManager
    participant MCP as MCP服务器
    participant Prep as 数据预处理器
    participant KB as 知识库
    participant LLM as 大语言模型
    
    User->>Agent: 提交查询请求
    Agent->>Exp: 检索相似经验
    Exp-->>Agent: 返回相似经验和few-shot示例
    
    Agent->>Agent: 初始化MCP连接
    Agent->>LLM: 构建系统提示词(含经验指导)
    
    loop ReAct迭代循环 (最多3次)
        Agent->>LLM: 发送查询和上下文
        LLM-->>Agent: 返回思考和行动
        
        alt 工具调用
            Agent->>MCP: 调用统计查询工具
            MCP-->>Agent: 返回原始数据
            Agent->>Prep: 数据预处理
            Prep->>KB: 知识库匹配分析
            KB-->>Prep: 返回匹配结果
            Prep-->>Agent: 格式化数据和分析结果
        else 最终答案
            Agent->>Agent: 准备最终答案
        end
        
        Agent->>Agent: 更新scratchpad
    end
    
    Agent->>Exp: 保存执行经验
    Agent-->>User: 返回诊断结果
    
    Note over Agent,Exp: 经验包含：查询、工具使用、执行时间、成功状态等
```

## 数据预处理器工作流程

```mermaid
flowchart TD
    A[原始JSON查询结果] --> B[NetworkDataPreprocessor]
    B --> C[解析输出项_parse_output_item]
    
    C --> D{数据类型判断}
    D -->|TCP流数据| E[_parse_tcp_flow_data]
    D -->|TCP交易数据| F[_parse_tcp_transaction_data]
    
    E --> G[解析CSV数据]
    G --> H[构建指标字典]
    H --> I[字段翻译映射]
    
    F --> J[解析包序列图]
    J --> K[提取TCP标志位]
    K --> L[解析时间戳]
    L --> M[提取窗口大小等]
    
    I --> N[知识库匹配分析<br/>analyze_knowledge_base_matches]
    M --> N
    
    N --> O[标准化字段名]
    O --> P[评估表达式<br/>evaluate_expr]
    P --> Q{匹配结果分类}
    
    Q -->|完全匹配| R[full_matches]
    Q -->|部分匹配| S[partial_matches]
    Q -->|需要更多信息| T[need_more_info]
    Q -->|不匹配| U[unmatched]
    
    R --> V[生成补充查询<br/>_generate_additional_query]
    S --> V
    T --> V
    U --> V
    
    V --> W[格式化输出文本<br/>_generate_formatted_text]
    W --> X[会话基本信息]
    W --> Y[TCP会话指标]
    W --> Z[在线解码结果]
    
    X --> AA[最终格式化结果]
    Y --> AA
    Z --> AA
    
    style A fill:#e1f5fe
    style AA fill:#c8e6c9
    style N fill:#fff3e0
    style V fill:#ffecb3
```

## MCP服务器架构与API交互
```mermaid
graph LR
    subgraph "MCP客户端 (Dify Plugin)"
        CLIENT[MCP Client]
        STDIO[Stdio Client]
        SSE[SSE Client]
        HTTP[HTTP Client]
    end
    
    subgraph "MCP服务器"
        SERVER[stats_mcp_server_official]
        TOOLS[MCP Tools]
        RESOURCES[MCP Resources]
        PROMPTS[MCP Prompts]
    end
    
    subgraph "API客户端层"
        API[StatsApiClient]
        AUTH[认证管理]
        PARSER[二进制解析器]
    end
    
    subgraph "统计API服务"
        LOGIN[登录接口]
        QUERY[统计查询接口]
        CONFIG[配置接口]
        TABLES[表枚举接口]
    end
    
    subgraph "数据库层"
        STATS[(统计数据库)]
        TCP[(TCP流数据)]
        DECODE[(解码数据)]
    end
    
    %% 连接关系
    CLIENT --> STDIO
    CLIENT --> SSE
    CLIENT --> HTTP
    
    STDIO -.->|stdio| SERVER
    SSE -.->|sse| SERVER
    HTTP -.->|http| SERVER
    
    SERVER --> TOOLS
    SERVER --> RESOURCES
    SERVER --> PROMPTS
    
    TOOLS --> API
    API --> AUTH
    API --> PARSER
    
    AUTH --> LOGIN
    TOOLS --> QUERY
    TOOLS --> CONFIG
    TOOLS --> TABLES
    
    QUERY --> STATS
    QUERY --> TCP
    QUERY --> DECODE
    
    %% 工具列表
    TOOLS --> T1[setup_api_connection]
    TOOLS --> T2[query_statistics_table]
    TOOLS --> T3[list_statistics_tables]
    TOOLS --> T4[list_table_fields]
    TOOLS --> T5[get_config]
    TOOLS --> T6[disconnect_api]
    
    %% 样式
    classDef clientLayer fill:#e3f2fd
    classDef serverLayer fill:#f3e5f5
    classDef apiLayer fill:#fff3e0
    classDef serviceLayer fill:#e8f5e8
    classDef dataLayer fill:#fce4ec
    
    class CLIENT,STDIO,SSE,HTTP clientLayer
    class SERVER,TOOLS,RESOURCES,PROMPTS serverLayer
    class API,AUTH,PARSER apiLayer
    class LOGIN,QUERY,CONFIG,TABLES serviceLayer
    class STATS,TCP,DECODE dataLayer
```

## 知识库匹配与诊断决策流程

```mermaid
flowchart TD
    A[用户查询数据] --> B[标准化字段名<br/>normalize_field_name]
    B --> C[知识库遍历]
    
    C --> D[NET-AVAIL_CONNECT-FAILED_1<br/>服务器无响应]
    C --> E[NET-AVAIL_CONNECT-FAILED_2<br/>SYN和SYNACK不匹配]
    C --> F[NET-AVAIL_CONNECT-FAILED_3<br/>SYN包收到RST]
    C --> G[NET-AVAIL_CONNECT-FAILED_4<br/>服务器端口不通]
    C --> H[NET-AVAIL_CONNECT-FAILED_5A<br/>防火墙静默丢弃]
    C --> I[NET-AVAIL_CONNECT-FAILED_5B<br/>防火墙拒绝]
    C --> J[NET-AVAIL_CONNECT-FAILED_6<br/>服务器负载过高]
    
    D --> K{字段覆盖检查}
    E --> K
    F --> K
    G --> K
    H --> K
    I --> K
    J --> K
    
    K -->|所有字段都有| L[条件表达式评估<br/>evaluate_expr]
    K -->|部分字段匹配| M[partial_matches]
    K -->|完全没有匹配| N[need_more_info]
    
    L --> O{条件是否满足}
    O -->|是| P[full_matches<br/>完全匹配]
    O -->|否| Q[unmatched<br/>不匹配]
    
    P --> R[直接输出诊断结论]
    M --> S[生成补充查询<br/>_generate_additional_query]
    N --> S
    Q --> S
    
    S --> T[TCP会话字段查询]
    S --> U[TCP解码查询]
    
    T --> V[执行二次查询]
    U --> V
    V --> W[更新数据集]
    W --> X[重新进行知识库匹配]
    X --> Y{二次匹配结果}
    
    Y -->|完全匹配| Z[输出最终诊断]
    Y -->|仍需补充| AA[继续迭代]
    
    AA --> S
    
    style A fill:#e1f5fe
    style R fill:#c8e6c9
    style Z fill:#c8e6c9
    style L fill:#fff3e0
    style S fill:#ffecb3
```

## 经验管理系统架构
```mermaid
graph TB
    subgraph "经验管理器 ExperienceManager"
        EM[ExperienceManager]
        EMBED[嵌入向量生成]
        SEARCH[相似性搜索]
        SAVE[经验保存]
    end
    
    subgraph "经验存储"
        EXP_FILE[react_experiences.json]
        VECTOR_DB[向量数据库<br/>Sentence Transformers]
    end
    
    subgraph "经验数据结构"
        QUERY[查询文本]
        ANSWER[最终答案]
        TOOLS[使用的工具]
        TIME[执行时间]
        TOKENS[Token使用量]
        SUCCESS[成功状态]
        ITERATIONS[迭代详情]
        METADATA[元数据]
    end
    
    subgraph "Few-Shot生成"
        SIMILAR[相似经验检索]
        TEMPLATE[模板生成]
        GUIDANCE[执行指导]
        EXAMPLES[示例构建]
    end
    
    subgraph "LLM总结系统"
        SUMMARIZER[LLMStepSummarizer]
        SUMMARY[步骤总结]
        INSIGHTS[洞察提取]
    end
    
    %% 连接关系
    EM --> EMBED
    EM --> SEARCH
    EM --> SAVE
    
    EMBED --> VECTOR_DB
    SEARCH --> VECTOR_DB
    SAVE --> EXP_FILE
    
    EXP_FILE --> QUERY
    EXP_FILE --> ANSWER
    EXP_FILE --> TOOLS
    EXP_FILE --> TIME
    EXP_FILE --> TOKENS
    EXP_FILE --> SUCCESS
    EXP_FILE --> ITERATIONS
    EXP_FILE --> METADATA
    
    SEARCH --> SIMILAR
    SIMILAR --> TEMPLATE
    TEMPLATE --> GUIDANCE
    TEMPLATE --> EXAMPLES
    
    EM --> SUMMARIZER
    SUMMARIZER --> SUMMARY
    SUMMARIZER --> INSIGHTS
    
    %% 工作流程
    SIMILAR -.-> EXAMPLES
    EXAMPLES -.-> GUIDANCE
    GUIDANCE -.-> TEMPLATE
    
    %% 样式
    classDef managerLayer fill:#e3f2fd
    classDef storageLayer fill:#f3e5f5
    classDef dataLayer fill:#fff3e0
    classDef generationLayer fill:#e8f5e8
    classDef summaryLayer fill:#fce4ec
    
    class EM,EMBED,SEARCH,SAVE managerLayer
    class EXP_FILE,VECTOR_DB storageLayer
    class QUERY,ANSWER,TOOLS,TIME,TOKENS,SUCCESS,ITERATIONS,METADATA dataLayer
    class SIMILAR,TEMPLATE,GUIDANCE,EXAMPLES generationLayer
    class SUMMARIZER,SUMMARY,INSIGHTS summaryLayer
```

